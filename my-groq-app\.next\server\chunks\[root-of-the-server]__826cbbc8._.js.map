{"version": 3, "sources": ["turbopack:///[project]/node_modules/node-domexception/index.js", "turbopack:///[project]/node_modules/formdata-node/lib/esm/fileFromPath.js", "turbopack:///[project]/node_modules/formdata-node/lib/esm/isPlainObject.js"], "sourcesContent": ["/*! node-domexception. MIT License. Jimmy <PERSON> <https://jimmy.warting.se/opensource> */\n\nif (!globalThis.DOMException) {\n  try {\n    const { MessageChannel } = require('worker_threads'),\n    port = new MessageChannel().port1,\n    ab = new ArrayBuffer()\n    port.postMessage(ab, [ab, ab])\n  } catch (err) {\n    err.constructor.name === 'DOMException' && (\n      globalThis.DOMException = err.constructor\n    )\n  }\n}\n\nmodule.exports = globalThis.DOMException\n", "var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _FileFromPath_path, _FileFromPath_start;\nimport { statSync, createReadStream, promises as fs } from \"fs\";\nimport { basename } from \"path\";\nimport DOMException from \"node-domexception\";\nimport { File } from \"./File.js\";\nimport isPlainObject from \"./isPlainObject.js\";\nexport * from \"./isFile.js\";\nconst MESSAGE = \"The requested file could not be read, \"\n    + \"typically due to permission problems that have occurred after a reference \"\n    + \"to a file was acquired.\";\nclass FileFromPath {\n    constructor(input) {\n        _FileFromPath_path.set(this, void 0);\n        _FileFromPath_start.set(this, void 0);\n        __classPrivateFieldSet(this, _FileFromPath_path, input.path, \"f\");\n        __classPrivateFieldSet(this, _FileFromPath_start, input.start || 0, \"f\");\n        this.name = basename(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"));\n        this.size = input.size;\n        this.lastModified = input.lastModified;\n    }\n    slice(start, end) {\n        return new FileFromPath({\n            path: __classPrivateFieldGet(this, _FileFromPath_path, \"f\"),\n            lastModified: this.lastModified,\n            size: end - start,\n            start\n        });\n    }\n    async *stream() {\n        const { mtimeMs } = await fs.stat(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"));\n        if (mtimeMs > this.lastModified) {\n            throw new DOMException(MESSAGE, \"NotReadableError\");\n        }\n        if (this.size) {\n            yield* createReadStream(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"), {\n                start: __classPrivateFieldGet(this, _FileFromPath_start, \"f\"),\n                end: __classPrivateFieldGet(this, _FileFromPath_start, \"f\") + this.size - 1\n            });\n        }\n    }\n    get [(_FileFromPath_path = new WeakMap(), _FileFromPath_start = new WeakMap(), Symbol.toStringTag)]() {\n        return \"File\";\n    }\n}\nfunction createFileFromPath(path, { mtimeMs, size }, filenameOrOptions, options = {}) {\n    let filename;\n    if (isPlainObject(filenameOrOptions)) {\n        [options, filename] = [filenameOrOptions, undefined];\n    }\n    else {\n        filename = filenameOrOptions;\n    }\n    const file = new FileFromPath({ path, size, lastModified: mtimeMs });\n    if (!filename) {\n        filename = file.name;\n    }\n    return new File([file], filename, {\n        ...options, lastModified: file.lastModified\n    });\n}\nexport function fileFromPathSync(path, filenameOrOptions, options = {}) {\n    const stats = statSync(path);\n    return createFileFromPath(path, stats, filenameOrOptions, options);\n}\nexport async function fileFromPath(path, filenameOrOptions, options) {\n    const stats = await fs.stat(path);\n    return createFileFromPath(path, stats, filenameOrOptions, options);\n}\n", "const getType = (value) => (Object.prototype.toString.call(value).slice(8, -1).toLowerCase());\nfunction isPlainObject(value) {\n    if (getType(value) !== \"object\") {\n        return false;\n    }\n    const pp = Object.getPrototypeOf(value);\n    if (pp === null || pp === undefined) {\n        return true;\n    }\n    const Ctor = pp.constructor && pp.constructor.toString();\n    return Ctor === Object.toString();\n}\nexport default isPlainObject;\n"], "names": [], "mappings": "iOAEA,GAAI,CAAC,WAAW,YAAY,CAC1B,CAD4B,EACxB,CACF,GAAM,CAAE,gBAAc,CAAE,CAAA,EAAA,CAAA,CAAA,OACxB,EAAO,IAAI,IAAiB,KAAK,CACjC,EAAK,IAAI,YACT,EAAK,WAAW,CAAC,EAAI,CAAC,EAAI,EAAG,CAC/B,CAAE,MAAO,EAAK,CACZ,AAAyB,kBAAkB,CAAvC,WAAW,CAAC,IAAI,GAClB,WAAW,YAAY,CAAG,EAAI,WAAA,AAChC,CACF,CAGF,EAAO,OAAO,CAAG,WAAW,YAAY,qKCHxC,IADI,EAAoB,EACxB,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,aCdA,SAAS,AAAc,CAAK,EACxB,GAAI,AAAmB,UAAU,CAFT,KAYb,EAZoB,SAAS,CAAC,QAAQ,CAAC,IAAI,CAE1C,AAF2C,GAAO,KAAK,CAAC,EAAG,CAAC,GAAG,WAAW,GAGlF,OAAO,EAEX,IAAM,EAAK,OAAO,cAAc,CAAC,UACjC,MAAI,GAIG,CADM,EAAG,CAHL,QAAQ,EAGQ,EAAI,EAAG,CAHR,UAGmB,CAAC,AAHT,QAGiB,EAAA,IACtC,OAAO,QAAQ,EACnC,EDMA,IAAA,EAAA,EAAA,CAAA,CAAA,OAjBI,EAAkE,SAAU,CAAQ,CAAE,CAAK,CAAE,CAAK,CAAE,CAAI,CAAE,CAAC,EAC3G,GAAa,AADY,MACrB,EAAc,MAAM,AAAI,UAAU,oBADJ,cAElC,GAAa,MAAT,GAAgB,CAAC,EAAG,MAAM,AAAI,MAFK,IAEK,kBAFiB,+BAG7D,GAAqB,AAAjB,mBAAO,EAAuB,IAAa,GAAS,CAAC,EAAI,CAAC,EAAM,GAAG,CAAC,GAAW,MAAM,AAAI,UAAU,2EACvG,MAAiB,MAAT,EAAe,EAAE,IAAI,CAAC,EAAU,GAAS,EAAI,EAAE,KAAK,CAAG,EAAQ,EAAM,GAAG,CAAC,EAAU,GAAS,CACxG,EACI,EAAkE,SAAU,CAAQ,CAAE,CAAK,CAAE,CAAI,CAAE,CAAC,EACpG,GAAa,EADY,IACrB,GAAgB,CAAC,EAAG,MAAM,AAAI,UAAU,kBADV,+BAElC,GAAqB,OAFkB,KAEnC,OAAO,EAAuB,IAAa,GAAS,CAAC,AAFI,EAEA,CAAC,EAAM,GAAG,CAAC,GAAW,MAAM,AAAI,UAAU,4EACvG,MAAgB,MAAT,EAAe,EAAa,MAAT,EAAe,EAAE,IAAI,CAAC,GAAY,EAAI,EAAE,KAAK,CAAG,EAAM,GAAG,CAAC,EACxF,CAWA,OAAM,EACF,YAAY,CAAK,CAAE,CACf,EAAmB,GAAG,CAAC,IAAI,CAAE,KAAK,GAClC,EAAoB,GAAG,CAAC,IAAI,CAAE,KAAK,GACnC,EAAuB,IAAI,CAAE,EAAoB,EAAM,IAAI,CAAE,KAC7D,EAAuB,IAAI,CAAE,EAAqB,EAAM,KAAK,EAAI,EAAG,KACpE,IAAI,CAAC,IAAI,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAuB,IAAI,CAAE,EAAoB,MACtE,IAAI,CAAC,IAAI,CAAG,EAAM,IAAI,CACtB,IAAI,CAAC,YAAY,CAAG,EAAM,YAAY,AAC1C,CACA,MAAM,CAAK,CAAE,CAAG,CAAE,CACd,OAAO,IAAI,EAAa,CACpB,KAAM,EAAuB,IAAI,CAAE,EAAoB,KACvD,aAAc,IAAI,CAAC,YAAY,CAC/B,KAAM,EAAM,QACZ,CACJ,EACJ,CACA,OAAO,QAAS,CACZ,GAAM,SAAE,CAAO,CAAE,CAAG,MAAM,EAAA,QAAE,CAAC,IAAI,CAAC,EAAuB,IAAI,CAAE,EAAoB,MACnF,GAAI,EAAU,IAAI,CAAC,YAAY,CAC3B,CAD6B,KACvB,IAAI,EAAA,OAAY,CAAC,AAxBnB,2CACV,+EACA,gBAsBsC,oBAEhC,IAAI,CAAC,IAAI,EAAE,CACX,MAAO,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAuB,IAAI,CAAE,EAAoB,KAAM,CAC3E,MAAO,EAAuB,IAAI,CAAE,EAAqB,KACzD,IAAK,EAAuB,IAAI,CAAE,EAAqB,KAAO,IAAI,CAAC,IAAI,CAAG,CAC9E,EAAA,CAER,CACA,GAAI,CAAC,CAAC,EAAqB,IAAI,QAAW,EAAsB,IAAI,QAAW,OAAO,WAAA,AAAW,EAAE,EAAG,CAClG,MAAO,MACX,CACJ,CACA,SAAS,EAAmB,CAAI,CAAE,SAAE,CAAO,MAAE,CAAI,CAAE,CAAE,CAAiB,CAAE,EAAU,CAAC,CAAC,MAC5E,EACA,EAAc,GACd,CAAC,EAAS,EAAS,CAAG,CAAC,OAAmB,EAAU,CADlB,AAIlC,EAAW,EAEf,IAAM,EAAO,IAAI,EAAa,MAAE,OAAM,EAAM,aAAc,CAAQ,GAIlE,OAHI,AAAC,IACD,EAAW,EAAK,EADL,EACK,AAAI,EAEjB,IAAI,EAAA,IAAI,CAAC,CAAC,EAAK,CAAE,EAAU,CAC9B,GAAG,CAAO,CAAE,aAAc,EAAK,YAAY,AAC/C,EACJ,CACO,SAAS,EAAiB,CAAI,CAAE,CAAiB,CAAE,EAAU,CAAC,CAAC,EAClE,IAAM,EAAQ,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,GACvB,OAAO,EAAmB,EAAM,EAAO,EAAmB,EAC9D,CACO,eAAe,EAAa,CAAI,CAAE,CAAiB,CAAE,CAAO,EAC/D,IAAM,EAAQ,MAAM,EAAA,QAAE,CAAC,IAAI,CAAC,GAC5B,OAAO,EAAmB,EAAM,EAAO,EAAmB,EAC9D", "ignoreList": [0, 1, 2]}