module.exports=[14747,(t,e,r)=>{e.exports=t.x("path",()=>require("path"))},22734,(t,e,r)=>{e.exports=t.x("fs",()=>require("fs"))},37702,(t,e,r)=>{e.exports=t.x("worker_threads",()=>require("worker_threads"))},39802,(t,e,r)=>{if(!globalThis.DOMException)try{let{MessageChannel:e}=t.r(37702),r=new e().port1,i=new ArrayBuffer;r.postMessage(i,[i,i])}catch(t){"DOMException"===t.constructor.name&&(globalThis.DOMException=t.constructor)}e.exports=globalThis.DOMException},18870,t=>{"use strict";t.s(["fileFromPath",()=>p,"fileFromPathSync",()=>u,"isFile",()=>w.isFile],18870),t.s(["fileFromPath",()=>p,"fileFromPathSync",()=>u],89362);var e,r,i=t.i(22734),o=t.i(14747),a=t.i(39802),s=t.i(56493);let n=function(t){if("object"!==Object.prototype.toString.call(t).slice(8,-1).toLowerCase())return!1;let e=Object.getPrototypeOf(t);return null==e||(e.constructor&&e.constructor.toString())===Object.toString()};var l=t.i(54769),c=function(t,e,r,i,o){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?o.call(t,r):o?o.value=r:e.set(t,r),r},f=function(t,e,r,i){if("a"===r&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?i:"a"===r?i.call(t):i?i.value:e.get(t)};class h{constructor(t){e.set(this,void 0),r.set(this,void 0),c(this,e,t.path,"f"),c(this,r,t.start||0,"f"),this.name=(0,o.basename)(f(this,e,"f")),this.size=t.size,this.lastModified=t.lastModified}slice(t,r){return new h({path:f(this,e,"f"),lastModified:this.lastModified,size:r-t,start:t})}async *stream(){let{mtimeMs:t}=await i.promises.stat(f(this,e,"f"));if(t>this.lastModified)throw new a.default("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");this.size&&(yield*(0,i.createReadStream)(f(this,e,"f"),{start:f(this,r,"f"),end:f(this,r,"f")+this.size-1}))}get[(e=new WeakMap,r=new WeakMap,Symbol.toStringTag)](){return"File"}}function d(t,{mtimeMs:e,size:r},i,o={}){let a;n(i)?[o,a]=[i,void 0]:a=i;let l=new h({path:t,size:r,lastModified:e});return a||(a=l.name),new s.File([l],a,{...o,lastModified:l.lastModified})}function u(t,e,r={}){let o=(0,i.statSync)(t);return d(t,o,e,r)}async function p(t,e,r){let o=await i.promises.stat(t);return d(t,o,e,r)}t.i(89362);var w=l}];

//# sourceMappingURL=%5Broot-of-the-server%5D__826cbbc8._.js.map