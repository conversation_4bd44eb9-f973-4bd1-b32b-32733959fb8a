module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},62397,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(87924),c=a.i(72131),d=a.i(73885),e=a.i(56283),f=a.i(71987),g=a.i(9453);function h(){let{isDarkMode:a}=(0,g.useTheme)(),[h,i]=(0,c.useState)([]),[j,k]=(0,c.useState)(""),[l,m]=(0,c.useState)(!1);(0,c.useRef)(null);let n=(0,c.useRef)(null);(0,c.useEffect)(()=>{n.current&&(n.current.scrollTop=n.current.scrollHeight)},[h]);let o=async a=>{if(a.preventDefault(),!j.trim()||l)return;let b={id:Date.now().toString(),role:"user",content:j,timestamp:new Date().toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})};i(a=>[...a,b]),k(""),m(!0),setTimeout(()=>{let a={id:(Date.now()+1).toString(),role:"assistant",content:"This is a placeholder response from PIP FTUI assistant. The actual RAG functionality is in development.",timestamp:new Date().toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})};i(b=>[...b,a]),m(!1)},1e3)};return(0,b.jsxs)("div",{className:"min-h-screen flex flex-col relative overflow-x-hidden",children:[(0,b.jsxs)("div",{className:"fixed inset-0 w-full h-full -z-10",children:[(0,b.jsx)("div",{className:`absolute inset-0 transition-colors duration-300 ${a?"bg-transparent":"bg-white"}`}),(0,b.jsx)("div",{className:`absolute inset-0 transition-all duration-300 ${a?"brightness-[0.4]":""}`,children:(0,b.jsx)(f.default,{src:"/Home_Page/Backround Design.svg",alt:"Background with yellow circles",fill:!0,className:"object-cover",priority:!0,quality:100})})]}),(0,b.jsx)(d.default,{}),(0,b.jsx)("div",{className:"flex flex-col pt-20 pb-4 px-8 min-h-screen",children:(0,b.jsxs)("div",{className:"w-full flex flex-col gap-4 mx-auto mb-4",style:{maxWidth:"calc(100% - 4rem)",minHeight:"calc(100vh - 9rem)"},children:[(0,b.jsx)("div",{className:`flex-1 backdrop-blur-sm rounded-[32px] shadow-[0_8px_32px_rgba(0,0,0,0.3)] p-8 overflow-hidden transition-colors duration-300 ${a?"bg-gradient-to-br from-[#1e3a5f]/90 via-[#2d4a6e]/90 to-[#3d5a7e]/90":"bg-gradient-to-br from-[#5a6c7d]/90 via-[#5a7a9d]/90 to-[#4a6b8a]/90"}`,children:(0,b.jsxs)("div",{ref:n,className:"h-full overflow-y-auto space-y-4 pr-2 chat-scroll",children:[0===h.length?(0,b.jsx)("div",{className:"flex items-center justify-center h-full text-white/60 text-center",children:(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"💬"}),(0,b.jsx)("p",{className:"text-xl font-[family-name:var(--font-comfortaa)]",children:"Ask PIP..."})]})}):h.map(a=>(0,b.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`max-w-[75%] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)] ${"user"===a.role?"bg-[#f5e6a3] text-gray-900":"bg-[#e8eef5] text-gray-900"}`,children:[(0,b.jsx)("div",{className:"text-[15px] leading-relaxed whitespace-pre-wrap font-[family-name:var(--font-quicksand)]",children:a.content}),a.timestamp&&(0,b.jsx)("div",{className:"text-[11px] text-gray-500 mt-2 text-right font-[family-name:var(--font-quicksand)]",children:a.timestamp}),a.attachments&&a.attachments.length>0&&(0,b.jsx)("div",{className:"mt-3 space-y-2",children:a.attachments.map((a,c)=>(0,b.jsxs)("div",{className:"bg-yellow-400 rounded-lg p-3 flex items-center gap-3",children:[(0,b.jsx)("div",{className:"text-3xl",children:"📄"}),(0,b.jsxs)("div",{className:"flex-grow",children:[(0,b.jsx)("p",{className:"font-semibold text-sm",children:a.name}),(0,b.jsx)("p",{className:"text-xs text-gray-600",children:a.size})]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)("button",{className:"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium",children:"View File"}),(0,b.jsx)("button",{className:"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium",children:"Download File"})]})]},c))})]})},a.id)),l&&(0,b.jsx)("div",{className:"flex justify-start",children:(0,b.jsx)("div",{className:"bg-[#e8eef5] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)]",children:(0,b.jsx)("div",{className:"flex items-center space-x-2",children:(0,b.jsxs)("div",{className:"flex space-x-1",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce"}),(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,b.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})})})]})}),(0,b.jsx)("form",{onSubmit:o,className:"relative flex-shrink-0",children:(0,b.jsxs)("div",{className:`rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4 transition-colors duration-300 ${a?"bg-gradient-to-r from-[#1e3a5f] via-[#2d4a6e] to-[#3d5a7e]":"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a]"}`,children:[(0,b.jsx)("input",{value:j,onChange:a=>k(a.target.value),placeholder:"ASK PIP...",disabled:l,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,b.jsx)("button",{type:"submit",disabled:l||!j.trim(),className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]})}),(0,b.jsx)(e.default,{})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__1b0e92d1._.js.map