{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/node_modules/next/src/client/components/handle-isr-error.tsx", "turbopack:///[project]/node_modules/next/src/client/components/builtin/global-error.tsx", "turbopack:///[project]/src/components/RAGPage.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n", "'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n", "'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\n\r\ninterface Message {\r\n  id: string;\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  sources?: string[];\r\n}\r\n\r\ninterface Document {\r\n  name: string;\r\n}\r\n\r\nexport default function RAGPage() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [documents, setDocuments] = useState<Document[]>([]);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [showUpload, setShowUpload] = useState(false);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  useEffect(() => {\r\n    fetchDocuments();\r\n  }, []);\r\n\r\n  const fetchDocuments = async () => {\r\n    try {\r\n      const response = await fetch('/api/documents');\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setDocuments(data.documents.map((name: string) => ({ name })));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching documents:', error);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    setIsUploading(true);\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    try {\r\n      const response = await fetch('/api/documents', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        await fetchDocuments();\r\n        alert('Document uploaded successfully!');\r\n        setShowUpload(false);\r\n      } else {\r\n        const error = await response.json();\r\n        alert(`Upload failed: ${error.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error uploading file:', error);\r\n      alert('Failed to upload document');\r\n    } finally {\r\n      setIsUploading(false);\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = '';\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() || isLoading) return;\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      role: 'user',\r\n      content: input,\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await fetch('/api/rag', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          question: input,\r\n          top_k: 3,\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch response');\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      const assistantMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: data.answer,\r\n        sources: data.sources,\r\n      };\r\n\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n    } catch (error) {\r\n      console.error('Error:', error);\r\n      const errorMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: 'Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running.',\r\n      };\r\n      setMessages(prev => [...prev, errorMessage]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\" style={{ background: 'linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)' }}>\r\n      <Navbar />\r\n      \r\n      <div className=\"mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-8\">\r\n          <div>\r\n            <h1 className=\"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n              PIP FTUI - RAG Mode\r\n            </h1>\r\n            <h2 className=\"text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]\">\r\n              Retrieval-Augmented Generation\r\n            </h2>\r\n          </div>\r\n          <p className=\"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]\">\r\n            Cari informasi FTUI berdasarkan dokumen yang tersedia\r\n          </p>\r\n          {/* Mode Switcher */}\r\n          <div className=\"flex justify-center gap-4 mt-6\">\r\n            <Link href=\"/chat\">\r\n              <div className=\"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n                💬 Chat Mode\r\n              </div>\r\n            </Link>\r\n            <div className=\"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n              📚 RAG Mode\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex gap-6\">\r\n          {/* Sidebar - Documents */}\r\n          <div className=\"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h2 className=\"text-xl font-bold text-gray-800\">Documents</h2>\r\n              <button\r\n                onClick={() => setShowUpload(!showUpload)}\r\n                className=\"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\r\n              >\r\n                {showUpload ? 'Cancel' : '+ Upload'}\r\n              </button>\r\n            </div>\r\n\r\n            {showUpload && (\r\n              <div className=\"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300\">\r\n                <input\r\n                  ref={fileInputRef}\r\n                  type=\"file\"\r\n                  onChange={handleFileUpload}\r\n                  disabled={isUploading}\r\n                  accept=\".pdf,.txt,.docx,.md,.html\"\r\n                  className=\"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer\"\r\n                />\r\n                {isUploading && (\r\n                  <p className=\"text-sm text-purple-600 mt-2\">Uploading...</p>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"overflow-y-auto h-[calc(100%-100px)]\">\r\n              {documents.length === 0 ? (\r\n                <div className=\"text-center text-gray-500 mt-8\">\r\n                  <div className=\"text-4xl mb-2\">📄</div>\r\n                  <p className=\"text-sm\">No documents yet</p>\r\n                  <p className=\"text-xs text-gray-400 mt-1\">Upload a document to start</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  {documents.map((doc, idx) => (\r\n                    <div\r\n                      key={idx}\r\n                      className=\"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors\"\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <span className=\"text-2xl\">📄</span>\r\n                        <span className=\"text-sm text-gray-700 truncate flex-1\">\r\n                          {doc.name}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Main Chat Area */}\r\n          <div className=\"flex-1\">\r\n            {/* Chat Messages */}\r\n            <div ref={chatContainerRef} className=\"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200\">\r\n              <div className=\"p-6 space-y-6\">\r\n                {messages.length === 0 ? (\r\n                  <div className=\"text-center text-[#346ad5] mt-8\">\r\n                    <div className=\"text-6xl mb-4\">📚</div>\r\n                    <p className=\"text-lg font-semibold\">Tanyakan tentang informasi FTUI</p>\r\n                    <p className=\"text-sm\">Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan.</p>\r\n                  </div>\r\n                ) : (\r\n                  messages.map((message) => (\r\n                    <div\r\n                      key={message.id}\r\n                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n                    >\r\n                      <div\r\n                        className={`\r\n                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md\r\n                          ${message.role === 'user'\r\n                            ? 'bg-[#346ad5] text-white'\r\n                            : 'bg-white text-gray-800 border border-gray-200'}\r\n                        `}\r\n                      >\r\n                        <div className={`text-xs mb-2 font-medium ${\r\n                          message.role === 'user' ? 'text-blue-100' : 'text-[#346ad5]'\r\n                        }`}>\r\n                          {message.role === 'user' ? 'You' : 'PIP RAG Assistant'}\r\n                        </div>\r\n                        <div className={`text-sm leading-relaxed whitespace-pre-wrap ${\r\n                          message.role === 'user' ? 'text-white' : 'text-gray-700'\r\n                        }`}>\r\n                          {message.content}\r\n                        </div>\r\n                        {message.sources && message.sources.length > 0 && (\r\n                          <div className=\"mt-4 pt-4 border-t border-gray-300\">\r\n                            <p className=\"text-xs font-semibold text-gray-600 mb-2\">Sources:</p>\r\n                            <div className=\"space-y-2\">\r\n                              {message.sources.map((source, idx) => (\r\n                                <div key={idx} className=\"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200\">\r\n                                  {source}\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                )}\r\n                {isLoading && (\r\n                  <div className=\"flex justify-start\">\r\n                    <div className=\"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200\">\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <div className=\"flex space-x-1\">\r\n                          <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\"></div>\r\n                          <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                          <div className=\"w-2 h-2 bg-[#fae664] rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                        </div>\r\n                        <span className=\"text-sm text-[#346ad5]\">Mencari di dokumen...</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Input Form */}\r\n            <form onSubmit={handleSubmit} className=\"relative flex-shrink-0\">\r\n              <div className=\"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4\">\r\n                <input\r\n                  value={input}\r\n                  onChange={(e) => setInput(e.target.value)}\r\n                  placeholder=\"ASK PIP...\"\r\n                  disabled={isLoading || documents.length === 0}\r\n                  className=\"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed\"\r\n                />\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={isLoading || !input.trim() || documents.length === 0}\r\n                  className=\"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]\"\r\n                  aria-label=\"Send message\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext", "ReactServerDOMTurbopackClient", "HooksClientContext", "ReactDOM", "ServerInsertedHtml", "HandleISRError", "workAsyncStorage", "window", "undefined", "error", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p"], "mappings": "+iBAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACC,gBAAgB,+BCFvCJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEE,6BAA6B,+BCFtDL,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACG,kBAAkB,+BCFzCN,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEI,QAAQ,+BCFjCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACK,kBAAkB,wGCQzBC,iBAAAA,qCAAAA,KAVhB,IAAMC,EAGER,EAAQ,CAAA,CAAA,IAAA,GACRQ,MAHN,OAAOC,GAGe,CAMjB,EALDC,KAJc,EASJH,EAAe,CAAyB,EAAzB,GAAA,OAAEI,CAAK,CAAkB,CAAzB,EAC7B,GAAIH,EAAkB,CACpB,IAAMI,EAAQJ,EAAiBK,QAAQ,GACvC,GAAID,CAAAA,QAAAA,KAAAA,EAAAA,EAAOE,YAAAA,AAAY,IAAIF,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAOG,kBAAAA,AAAkB,EAElD,CAFoD,KACpDC,QAAQL,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,+TCgCA,OADA,AADA,GAEA,qCAAA,GAD2C,uBAjDZ,CAAA,CAAA,IAAA,GAEzBM,EAAS,CACbN,EA6C8E,IA7CvE,CAELO,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA8BA,EAzBA,SAASC,AAAmB,AAyBbA,CAzBsC,EAAzB,GAAA,CAAEnB,OAAK,CAAkB,CAAzB,EACpBoB,EAA6BpB,MAAAA,EAAAA,KAAAA,EAAAA,EAAOoB,MAAM,CAChD,MACE,CAAA,AADF,EACE,EAAA,IAAA,EAACC,CADH,MACGA,CAAKC,GAAG,2BACP,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAACC,OAAAA,WACC,GAAA,EAAA,GAAA,EAAC5B,EAAAA,cAAc,CAAA,CAACI,MAAOA,IACvB,CAAA,EAAA,EAAA,GAAA,EAACyB,MAAAA,CAAIC,MAAOpB,EAAON,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACyB,CAAD,KAACA,WACC,GAAA,EAAA,IAAA,EAACE,KAAAA,CAAGD,MAAOpB,EAAOQ,IAAI,WAAE,wBACAM,EAAS,SAAW,SAAS,8CACvBtB,OAAO8B,QAAQ,CAACC,QAAQ,CAAC,YAAU,IAC9DT,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAS,CAAA,EAAA,EAAA,EAATA,CAAS,EAACU,IAAAA,CAAEJ,GAAZN,GAAmBd,EAAOQ,IAAI,UAAI,WAAUM,IAAgB,eAMzE,wRC9CA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAae,SAAS,IACtB,GAAM,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAa,EAAE,EACnD,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,GAAS,GACzC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACvC,EAAmB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAC1C,EAAe,CAAA,EAAA,EAAA,MAAM,AAAN,EAAyB,MAQ9C,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KALJ,EAAiB,OAAO,EAAE,CAC5B,EAAiB,OAAO,CAAC,SAAS,CAAG,EAAiB,OAAO,CAAC,YAAA,AAAY,CAM9E,EAAG,CAAC,EAAS,EAEb,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,GACF,EAAG,EAAE,EAEL,IAAM,EAAiB,UACrB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,kBAC7B,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAa,EAAK,SAAS,CAAC,GAAG,CAAC,AAAC,IAAkB,GAAD,GAAG,EAAK,CAAC,EAC7D,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,4BAA6B,EAC7C,CACF,EAEM,EAAmB,MAAO,IAC9B,IAAM,EAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAChC,GAAI,CAAC,EAAM,OAEX,GAAe,GACf,IAAM,EAAW,IAAI,SACrB,EAAS,MAAM,CAAC,OAAQ,GAExB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,iBAAkB,CAC7C,OAAQ,OACR,KAAM,CACR,GAEA,GAAI,EAAS,EAAE,CACb,CADe,KACT,IACN,MAAM,mCACN,GAAc,OACT,CACL,IAAM,EAAQ,MAAM,EAAS,IAAI,GACjC,MAAM,CAAC,eAAe,EAAE,EAAM,KAAK,CAAA,CAAE,CACvC,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wBAAyB,GACvC,MAAM,4BACR,QAAU,CACR,GAAe,GACX,EAAa,OAAO,EAAE,CACxB,EAAa,OAAO,CAAC,KAAK,CAAG,EAAA,CAEjC,CACF,EAEM,EAAe,MAAO,IAE1B,GADA,EAAE,cAAc,GACZ,CAAC,EAAM,IAAI,IAAM,EAAW,OAEhC,IAAM,EAAuB,CAC3B,GAAI,KAAK,GAAG,GAAG,QAAQ,GACvB,KAAM,OACN,QAAS,CACX,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAY,EAC1C,EAAS,IACT,GAAa,GAEb,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,WAAY,CACvC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,EACV,MAAO,CACT,EACF,GAEA,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,4BAGlB,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAA4B,CAChC,GAAI,CAAC,KAAK,GAAG,GAAK,CAAC,EAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,EAAK,MAAM,CACpB,QAAS,EAAK,OAAO,AACvB,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAiB,CACjD,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,SAAU,GACxB,IAAM,EAAwB,CAC5B,GAAI,CAAC,KAAK,GAAG,IAAK,CAAC,CAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,yGACX,EACA,EAAY,GAAQ,IAAI,EAAM,EAAa,CAC7C,QAAU,CACR,GAAa,EACf,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BAA6B,MAAO,CAAE,WAAY,gEAAiE,YAChI,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,GAEP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uFAA8E,wBAG5F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,sCAIjG,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oFAA2E,0DAIxF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,iBACT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+KAAsK,mBAIvL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uHAA8G,sBAMjI,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qFACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2CAAkC,cAChD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAc,CAAC,GAC9B,UAAU,mHAET,EAAa,SAAW,gBAI5B,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,IAAK,EACL,KAAK,OACL,SAAU,EACV,SAAU,EACV,OAAO,4BACP,UAAU,6MAEX,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,wCAA+B,oBAKlD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gDACS,IAArB,EAAU,MAAM,CACf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,qBACvB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,kCAG5C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAU,GAAG,CAAC,CAAC,EAAK,IACnB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAU,gGAEV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oBAAW,OAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iDACb,EAAI,IAAI,OANR,WAiBjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mBAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,IAAK,EAAkB,UAAU,gGACpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACQ,IAApB,EAAS,MAAM,CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,oCACrC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,mEAGzB,EAAS,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAW,CAAC,KAAK,EAAE,AAAiB,WAAT,IAAI,CAAc,cAAgB,gBAAA,CAAiB,UAE9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAW,CAAC;;0BAEV,EAAmB,SAAjB,EAAQ,IAAI,CACV,0BACA,gDAAgD;wBACtD,CAAC,WAED,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,yBAAyB,EACxC,AAAiB,WAAT,IAAI,CAAc,gBAAkB,iBAAA,CAC5C,UACkB,SAAjB,EAAQ,IAAI,CAAc,MAAQ,sBAErC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,4CAA4C,EAC1C,SAAjB,EAAQ,IAAI,CAAc,aAAe,gBAAA,CACzC,UACC,EAAQ,OAAO,GAEjB,EAAQ,OAAO,EAAI,EAAQ,OAAO,CAAC,MAAM,CAAG,GAC3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+CACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oDAA2C,aACxD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,EAAQ,IAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAc,UAAU,6EACtB,GADO,aA1Bf,EAAQ,EAAE,GAqCpB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAAmD,MAAO,CAAE,eAAgB,MAAO,IAClG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAAmD,MAAO,CAAE,eAAgB,MAAO,OAEpG,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,kCAAyB,sCASrD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAU,EAAc,UAAU,kCACtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qJACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,YAAY,aACZ,SAAU,GAAkC,IAArB,EAAU,MAAM,CACvC,UAAU,iKAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAa,CAAC,EAAM,IAAI,IAA2B,IAArB,EAAU,MAAM,CACxD,UAAU,wNACV,aAAW,wBAEX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,eAAe,QAAQ,qBACnD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,4DAStB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,KAGb", "ignoreList": [0, 1, 2, 3, 4, 5, 6]}