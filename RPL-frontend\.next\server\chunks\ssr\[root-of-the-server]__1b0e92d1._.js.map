{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/node_modules/next/src/client/components/handle-isr-error.tsx", "turbopack:///[project]/node_modules/next/src/client/components/builtin/global-error.tsx", "turbopack:///[project]/src/components/HomePage.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n", "'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n", "'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\nimport Image from 'next/image';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\ninterface Message {\r\n  id: string;\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  timestamp?: string;\r\n  attachments?: {\r\n    name: string;\r\n    size: string;\r\n    type: string;\r\n  }[];\r\n}\r\n\r\nexport default function HomePage() {\r\n  const { isDarkMode } = useTheme();\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() || isLoading) return;\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      role: 'user',\r\n      content: input,\r\n      timestamp: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    // Simulate API response\r\n    setTimeout(() => {\r\n      const assistantMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: 'This is a placeholder response from PIP FTUI assistant. The actual RAG functionality is in development.',\r\n        timestamp: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),\r\n      };\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n      setIsLoading(false);\r\n    }, 1000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col relative overflow-x-hidden\">\r\n      {/* Background with Yellow Circles */}\r\n      <div className=\"fixed inset-0 w-full h-full -z-10\">\r\n        {/* White background layer for light mode */}\r\n        <div className={`absolute inset-0 transition-colors duration-300 ${\r\n          isDarkMode ? 'bg-transparent' : 'bg-white'\r\n        }`}></div>\r\n        {/* SVG overlay with brightness control */}\r\n        <div className={`absolute inset-0 transition-all duration-300 ${\r\n          isDarkMode ? 'brightness-[0.4]' : ''\r\n        }`}>\r\n          <Image\r\n            src=\"/Home_Page/Backround Design.svg\"\r\n            alt=\"Background with yellow circles\"\r\n            fill\r\n            className=\"object-cover\"\r\n            priority\r\n            quality={100}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <Navbar />\r\n      \r\n      <div className=\"flex flex-col pt-20 pb-4 px-8 min-h-screen\">\r\n        <div className=\"w-full flex flex-col gap-4 mx-auto mb-4\" style={{ maxWidth: 'calc(100% - 4rem)', minHeight: 'calc(100vh - 9rem)' }}>\r\n          {/* Chat Messages Container - Fixed height with scroll */}\r\n          <div className={`flex-1 backdrop-blur-sm rounded-[32px] shadow-[0_8px_32px_rgba(0,0,0,0.3)] p-8 overflow-hidden transition-colors duration-300 ${\r\n            isDarkMode \r\n              ? 'bg-gradient-to-br from-[#1e3a5f]/90 via-[#2d4a6e]/90 to-[#3d5a7e]/90' \r\n              : 'bg-gradient-to-br from-[#5a6c7d]/90 via-[#5a7a9d]/90 to-[#4a6b8a]/90'\r\n          }`}>\r\n            <div ref={chatContainerRef} className=\"h-full overflow-y-auto space-y-4 pr-2 chat-scroll\">\r\n              {messages.length === 0 ? (\r\n                <div className=\"flex items-center justify-center h-full text-white/60 text-center\">\r\n                  <div>\r\n                    <div className=\"text-6xl mb-4\">💬</div>\r\n                    <p className=\"text-xl font-[family-name:var(--font-comfortaa)]\">Ask PIP...</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                messages.map((message) => (\r\n                  <div\r\n                    key={message.id}\r\n                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n                  >\r\n                    <div\r\n                      className={`max-w-[75%] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)] ${\r\n                        message.role === 'user'\r\n                          ? 'bg-[#f5e6a3] text-gray-900'\r\n                          : 'bg-[#e8eef5] text-gray-900'\r\n                      }`}\r\n                    >\r\n                      <div className=\"text-[15px] leading-relaxed whitespace-pre-wrap font-[family-name:var(--font-quicksand)]\">\r\n                        {message.content}\r\n                      </div>\r\n                      {message.timestamp && (\r\n                        <div className=\"text-[11px] text-gray-500 mt-2 text-right font-[family-name:var(--font-quicksand)]\">\r\n                          {message.timestamp}\r\n                        </div>\r\n                      )}\r\n                      {message.attachments && message.attachments.length > 0 && (\r\n                        <div className=\"mt-3 space-y-2\">\r\n                          {message.attachments.map((attachment, idx) => (\r\n                            <div key={idx} className=\"bg-yellow-400 rounded-lg p-3 flex items-center gap-3\">\r\n                              <div className=\"text-3xl\">📄</div>\r\n                              <div className=\"flex-grow\">\r\n                                <p className=\"font-semibold text-sm\">{attachment.name}</p>\r\n                                <p className=\"text-xs text-gray-600\">{attachment.size}</p>\r\n                              </div>\r\n                              <div className=\"flex gap-2\">\r\n                                <button className=\"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium\">\r\n                                  View File\r\n                                </button>\r\n                                <button className=\"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium\">\r\n                                  Download File\r\n                                </button>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n              {isLoading && (\r\n                <div className=\"flex justify-start\">\r\n                  <div className=\"bg-[#e8eef5] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)]\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <div className=\"flex space-x-1\">\r\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"></div>\r\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                        <div className=\"w-2 h-2 bg-yellow-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Input Form - Compact spacing */}\r\n          <form onSubmit={handleSubmit} className=\"relative flex-shrink-0\">\r\n            <div className={`rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4 transition-colors duration-300 ${\r\n              isDarkMode\r\n                ? 'bg-gradient-to-r from-[#1e3a5f] via-[#2d4a6e] to-[#3d5a7e]'\r\n                : 'bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a]'\r\n            }`}>\r\n              <input\r\n                value={input}\r\n                onChange={(e) => setInput(e.target.value)}\r\n                placeholder=\"ASK PIP...\"\r\n                disabled={isLoading}\r\n                className=\"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed\"\r\n              />\r\n              <button\r\n                type=\"submit\"\r\n                disabled={isLoading || !input.trim()}\r\n                className=\"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]\"\r\n                aria-label=\"Send message\"\r\n              >\r\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path d=\"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n      \r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM", "AppRouterContext", "ReactServerDOMTurbopackClient", "HooksClientContext", "ServerInsertedHtml", "HandleISRError", "workAsyncStorage", "window", "undefined", "error", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p"], "mappings": "+iBAAAA,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEC,QAAQ,8BCFjCJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACE,gBAAgB,8BCFvCL,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEG,6BAA6B,8BCFtDN,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACI,kBAAkB,+BCFzCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACK,kBAAkB,wGCQzBC,iBAAAA,qCAAAA,KAVhB,IAAMC,EAGER,EAAQ,CAAA,CAAA,IAAA,GACRQ,MAHN,OAAOC,GAGe,CAMjB,EALDC,KAJc,EASJH,EAAe,CAAyB,EAAzB,GAAA,OAAEI,CAAK,CAAkB,CAAzB,EAC7B,GAAIH,EAAkB,CACpB,IAAMI,EAAQJ,EAAiBK,QAAQ,GACvC,GAAID,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOE,YAAAA,AAAY,IAAIF,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAOG,kBAAAA,AAAkB,EAElD,CAFoD,KACpDC,QAAQL,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,+TCgCA,OADA,AADA,GAEA,qCAAA,GAD2C,uBAjDZ,CAAA,CAAA,IAAA,GAEzBM,EAAS,CACbN,EA6C8E,IA7CvE,CAELO,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA8BA,EAzBA,SAASC,AAAmB,AAyBbA,CAzBsC,EAAzB,GAAA,OAAEnB,CAAK,CAAkB,CAAzB,EACpBoB,EAA6BpB,MAAAA,EAAAA,KAAAA,EAAAA,EAAOoB,MAAM,CAChD,MACE,CAAA,AADF,EACE,EAAA,IAAA,EAACC,CADH,MACGA,CAAKC,GAAG,2BACP,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAACC,OAAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC5B,EAAAA,cAAc,CAAA,CAACI,MAAOA,IACvB,CAAA,EAAA,EAAA,GAAA,EAACyB,MAAAA,CAAIC,MAAOpB,EAAON,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACyB,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,IAAA,EAACE,KAAAA,CAAGD,MAAOpB,EAAOQ,IAAI,WAAE,wBACAM,EAAS,SAAW,SAAS,8CACvBtB,OAAO8B,QAAQ,CAACC,QAAQ,CAAC,YAAU,IAC9DT,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAS,CAAA,EAAA,EAAA,EAATA,CAAS,EAACU,IAAAA,CAAEJ,GAAZN,GAAmBd,EAAOQ,IAAI,UAAI,WAAUM,IAAgB,eAMzE,yRC9CA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAce,SAAS,IACtB,GAAM,YAAE,CAAU,CAAE,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,IACzB,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAS,IAC7B,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACpB,CAAA,EAAA,EAAA,MAAM,AAAN,EAAuB,MAC9C,IAAM,EAAmB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAQhD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KALJ,EAAiB,OAAO,EAAE,CAC5B,EAAiB,OAAO,CAAC,SAAS,CAAG,EAAiB,OAAO,CAAC,YAAA,AAAY,CAM9E,EAAG,CAAC,EAAS,EAEb,IAAM,EAAe,MAAO,IAE1B,GADA,EAAE,cAAc,GACZ,CAAC,EAAM,IAAI,IAAM,EAAW,OAEhC,IAAM,EAAuB,CAC3B,GAAI,KAAK,GAAG,GAAG,QAAQ,GACvB,KAAM,OACN,QAAS,EACT,UAAW,IAAI,OAAO,kBAAkB,CAAC,QAAS,CAAE,KAAM,UAAW,OAAQ,SAAU,EACzF,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAY,EAC1C,EAAS,IACT,GAAa,GAGb,WAAW,KACT,IAAM,EAA4B,CAChC,GAAI,CAAC,KAAK,GAAG,IAAK,CAAC,CAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,0GACT,UAAW,IAAI,OAAO,kBAAkB,CAAC,QAAS,CAAE,KAAM,UAAW,OAAQ,SAAU,EACzF,EACA,EAAY,GAAQ,IAAI,EAAM,EAAiB,EAC/C,EAAa,GACf,EAAG,IACL,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kEAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,gDAAgD,EAC/D,EAAa,iBAAmB,WAAA,CAChC,GAEF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,6CAA6C,EAC5D,EAAa,mBAAqB,GAAA,CAClC,UACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,kCACJ,IAAI,iCACJ,IAAI,CAAA,CAAA,EACJ,UAAU,eACV,QAAQ,CAAA,CAAA,EACR,QAAS,WAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,GAEP,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0CAA0C,MAAO,CAAE,SAAU,oBAAqB,UAAW,oBAAqB,YAE/H,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,8HAA8H,EAC7I,EACI,uEACA,uEAAA,CACJ,UACA,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,IAAK,EAAkB,UAAU,8DACf,IAApB,EAAS,MAAM,CACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4DAAmD,oBAIpE,EAAS,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAW,CAAC,KAAK,EAAmB,SAAjB,EAAQ,IAAI,CAAc,cAAgB,gBAAA,CAAiB,UAE9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAW,CAAC,0EAA0E,EACnE,SAAjB,EAAQ,IAAI,CACR,6BACA,6BAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oGACZ,EAAQ,OAAO,GAEjB,EAAQ,SAAS,EAChB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8FACZ,EAAQ,SAAS,GAGrB,EAAQ,WAAW,EAAI,EAAQ,WAAW,CAAC,MAAM,CAAG,GACnD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0BACZ,EAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,EAAY,IACpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAc,UAAU,iEACvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oBAAW,OAC1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAyB,EAAW,IAAI,GACrD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAyB,EAAW,IAAI,MAEvD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,8FAAqF,cAGvG,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,8FAAqF,uBAVjG,UArBb,EAAQ,EAAE,GA2CpB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAkD,MAAO,CAAE,eAAgB,MAAO,IACjG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDAAoD,MAAO,CAAE,eAAgB,MAAO,kBAUjH,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAU,EAAc,UAAU,kCACtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,6GAA6G,EAC5H,EACI,6DACA,6DAAA,CACJ,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,MAAO,EACP,SAAW,AAAD,GAAO,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,YAAY,aACZ,SAAU,EACV,UAAU,iKAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAa,CAAC,EAAM,IAAI,GAClC,UAAU,wNACV,aAAW,wBAEX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,eAAe,QAAQ,qBACnD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,wDAQpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,KAGb", "ignoreList": [0, 1, 2, 3, 4, 5, 6]}