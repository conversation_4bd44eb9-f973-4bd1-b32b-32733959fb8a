{"version": 3, "sources": ["turbopack:///[project]/src/components/ContactPage.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/src/app/contacts/page.tsx"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContactPage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContactPage.tsx\",\n    \"default\",\n);\n", "import ContactPage from \"@/components/ContactPage\";\r\n\r\nexport default function Contact() {\r\n  return (\r\n    <ContactPage />\r\n  );\r\n}\r\n"], "names": [], "mappings": "sPAEe,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,gSAAkS,EAC/T,+DACA,gEAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,4QAA8Q,EAC3S,2CACA,uJCLJ,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAW,CAAA,CAAA,EAEhB", "ignoreList": [0]}