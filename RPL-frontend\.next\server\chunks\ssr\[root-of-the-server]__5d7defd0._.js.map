{"version": 3, "sources": ["turbopack:///[project]/src/components/RAGPage.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/src/app/rag/page.tsx"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/RAGPage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/RAGPage.tsx\",\n    \"default\",\n);\n", "import RAGPage from '@/components/RAGPage';\r\n\r\nexport default function RAG() {\r\n  return <RAGPage />;\r\n}\r\n"], "names": [], "mappings": "sPAEe,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,4RAA8R,EAC3T,2DACA,gEAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,wQAA0Q,EACvS,uCACA,uJCLJ,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAO,CAAA,CAAA,EACjB", "ignoreList": [0]}