module.exports=[72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40777,a=>{"use strict";a.s(["default",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(38246);function e(){let[a,e]=(0,c.useState)([]),[f,g]=(0,c.useState)(""),[h,i]=(0,c.useState)(!1),j=(0,c.useRef)(null);(0,c.useEffect)(()=>{j.current?.scrollIntoView({behavior:"smooth"})},[a]);let k=async b=>{if(b.preventDefault(),!f.trim()||h)return;let c={id:Date.now().toString(),role:"user",content:f};e(a=>[...a,c]),g(""),i(!0);let d={id:(Date.now()+1).toString(),role:"assistant",content:""};e(a=>[...a,d]);try{let b=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...a,c]})});if(!b.ok)throw Error("Failed to fetch response");let f=b.body?.getReader(),g=new TextDecoder;if(f)for(;;){let{done:a,value:b}=await f.read();if(a)break;for(let a of g.decode(b).split("\n"))if(a.startsWith("data: ")){let b=a.slice(6);if("[DONE]"===b)break;try{let a=JSON.parse(b);a.content&&e(b=>b.map(b=>b.id===d.id?{...b,content:b.content+a.content}:b))}catch(a){}}}}catch(a){console.error("Error:",a),e(a=>a.map(a=>a.id===d.id?{...a,content:"Sorry, I encountered an error. Please try again."}:a))}finally{i(!1)}};return(0,b.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,b.jsxs)("div",{className:"mx-auto w-full max-w-4xl py-8 px-4",children:[(0,b.jsxs)("div",{className:"text-center mb-8",children:[(0,b.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"PIP FTUI"}),(0,b.jsx)("p",{className:"text-gray-600 mb-4",children:"Powered by Llama 3.1 8B Instant"}),(0,b.jsxs)("div",{className:"flex justify-center gap-4 mt-4",children:[(0,b.jsx)("div",{className:"bg-blue-500 text-white px-6 py-2 rounded-lg font-medium",children:"💬 Chat Mode"}),(0,b.jsx)(d.default,{href:"/rag",children:(0,b.jsx)("div",{className:"bg-white text-gray-700 border-2 border-gray-300 px-6 py-2 rounded-lg font-medium hover:border-purple-500 hover:text-purple-600 transition-colors cursor-pointer",children:"📚 RAG Mode"})})]})]}),(0,b.jsx)("div",{className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,b.jsxs)("div",{className:"p-6 space-y-6",children:[0===a.length?(0,b.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"💬"}),(0,b.jsx)("p",{className:"text-lg",children:"Start a conversation!"}),(0,b.jsx)("p",{className:"text-sm",children:"Type your message below to begin chatting."})]}):a.map(a=>(0,b.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`
                      max-w-[80%] rounded-2xl px-6 py-4 shadow-md
                      ${"user"===a.role?"bg-gradient-to-r from-blue-500 to-blue-600 text-white":"bg-gray-100 text-gray-800 border border-gray-200"}
                    `,children:[(0,b.jsx)("div",{className:`text-xs mb-2 font-medium ${"user"===a.role?"text-blue-100":"text-gray-500"}`,children:"user"===a.role?"You":"Llama 3.1 8B Instant"}),(0,b.jsx)("div",{className:`text-sm leading-relaxed whitespace-pre-wrap ${"user"===a.role?"text-white":"text-gray-700"}`,children:a.content})]})},a.id)),h&&(0,b.jsx)("div",{className:"flex justify-start",children:(0,b.jsx)("div",{className:"bg-gray-100 rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)("div",{className:"flex space-x-1",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,b.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,b.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,b.jsx)("span",{className:"text-sm text-gray-500",children:"Thinking..."})]})})}),(0,b.jsx)("div",{ref:j})]})}),(0,b.jsxs)("form",{onSubmit:k,className:"flex gap-4",children:[(0,b.jsx)("div",{className:"flex-1 relative",children:(0,b.jsx)("input",{value:f,onChange:a=>g(a.target.value),placeholder:"Type your message...",disabled:h,className:"w-full rounded-2xl border-2 border-gray-200 px-6 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 disabled:bg-gray-50 disabled:cursor-not-allowed text-lg shadow-lg"})}),(0,b.jsx)("button",{type:"submit",disabled:h||!f.trim(),className:"rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 px-8 py-4 text-white font-medium hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-200",children:h?"Sending...":"Send"})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__56c3a78e._.js.map