module.exports=[23566,a=>{a.v({className:"quicksand_b7e087bf-module__3kZyda__className",variable:"quicksand_b7e087bf-module__3kZyda__variable"})},8391,a=>{a.v({className:"comfortaa_5e8afc88-module__lBtixG__className",variable:"comfortaa_5e8afc88-module__lBtixG__variable"})},85852,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/Providers.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/Providers.tsx <module evaluation>","default")},5884,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/Providers.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/Providers.tsx","default")},99799,a=>{"use strict";a.i(85852);var b=a.i(5884);a.n(b)},27572,a=>{"use strict";a.s(["default",()=>i,"metadata",()=>h],27572);var b=a.i(7997),c=a.i(23566);let d={className:c.default.className,style:{fontFamily:"'Quicksand', 'Quicksand Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(8391);let f={className:e.default.className,style:{fontFamily:"'Comfortaa', 'Comfortaa Fallback'",fontStyle:"normal"}};null!=e.default.variable&&(f.variable=e.default.variable);var g=a.i(99799);let h={title:"PIP FTUI - Pusat Informasi Publik",description:"AI Assistant untuk Informasi dan Layanan Fakultas Teknik Universitas Indonesia"};function i({children:a}){return(0,b.jsx)("html",{lang:"en",children:(0,b.jsx)("body",{className:`${d.variable} ${f.variable} antialiased`,children:(0,b.jsx)(g.default,{children:a})})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9364b62f._.js.map