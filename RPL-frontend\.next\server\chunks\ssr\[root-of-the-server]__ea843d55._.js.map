{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/node_modules/next/src/client/components/handle-isr-error.tsx", "turbopack:///[project]/node_modules/next/src/client/components/builtin/global-error.tsx", "turbopack:///[project]/src/components/HomePage.tsx", "turbopack:///[project]/src/components/ChatPage.tsx", "turbopack:///[project]/src/components/RAGPage.tsx", "turbopack:///[project]/src/components/ContactPage.tsx", "turbopack:///[project]/src/components/LandingPage.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n", "'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n", "'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\nimport Image from 'next/image';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\ninterface Message {\r\n  id: string;\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  timestamp?: string;\r\n  attachments?: {\r\n    name: string;\r\n    size: string;\r\n    type: string;\r\n  }[];\r\n}\r\n\r\nexport default function HomePage() {\r\n  const { isDarkMode } = useTheme();\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() || isLoading) return;\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      role: 'user',\r\n      content: input,\r\n      timestamp: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    // Simulate API response\r\n    setTimeout(() => {\r\n      const assistantMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: 'This is a placeholder response from PIP FTUI assistant. The actual RAG functionality is in development.',\r\n        timestamp: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),\r\n      };\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n      setIsLoading(false);\r\n    }, 1000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col relative overflow-x-hidden\">\r\n      {/* Background with Yellow Circles */}\r\n      <div className=\"fixed inset-0 w-full h-full -z-10\">\r\n        {/* White background layer for light mode */}\r\n        <div className={`absolute inset-0 transition-colors duration-300 ${\r\n          isDarkMode ? 'bg-transparent' : 'bg-white'\r\n        }`}></div>\r\n        {/* SVG overlay with brightness control */}\r\n        <div className={`absolute inset-0 transition-all duration-300 ${\r\n          isDarkMode ? 'brightness-[0.4]' : ''\r\n        }`}>\r\n          <Image\r\n            src=\"/Home_Page/Backround Design.svg\"\r\n            alt=\"Background with yellow circles\"\r\n            fill\r\n            className=\"object-cover\"\r\n            priority\r\n            quality={100}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <Navbar />\r\n      \r\n      <div className=\"flex flex-col pt-20 pb-4 px-8 min-h-screen\">\r\n        <div className=\"w-full flex flex-col gap-4 mx-auto mb-4\" style={{ maxWidth: 'calc(100% - 4rem)', minHeight: 'calc(100vh - 9rem)' }}>\r\n          {/* Chat Messages Container - Fixed height with scroll */}\r\n          <div className={`flex-1 backdrop-blur-sm rounded-[32px] shadow-[0_8px_32px_rgba(0,0,0,0.3)] p-8 overflow-hidden transition-colors duration-300 ${\r\n            isDarkMode \r\n              ? 'bg-gradient-to-br from-[#1e3a5f]/90 via-[#2d4a6e]/90 to-[#3d5a7e]/90' \r\n              : 'bg-gradient-to-br from-[#5a6c7d]/90 via-[#5a7a9d]/90 to-[#4a6b8a]/90'\r\n          }`}>\r\n            <div ref={chatContainerRef} className=\"h-full overflow-y-auto space-y-4 pr-2 chat-scroll\">\r\n              {messages.length === 0 ? (\r\n                <div className=\"flex items-center justify-center h-full text-white/60 text-center\">\r\n                  <div>\r\n                    <div className=\"text-6xl mb-4\">💬</div>\r\n                    <p className=\"text-xl font-[family-name:var(--font-comfortaa)]\">Ask PIP...</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                messages.map((message) => (\r\n                  <div\r\n                    key={message.id}\r\n                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n                  >\r\n                    <div\r\n                      className={`max-w-[75%] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)] ${\r\n                        message.role === 'user'\r\n                          ? 'bg-[#f5e6a3] text-gray-900'\r\n                          : 'bg-[#e8eef5] text-gray-900'\r\n                      }`}\r\n                    >\r\n                      <div className=\"text-[15px] leading-relaxed whitespace-pre-wrap font-[family-name:var(--font-quicksand)]\">\r\n                        {message.content}\r\n                      </div>\r\n                      {message.timestamp && (\r\n                        <div className=\"text-[11px] text-gray-500 mt-2 text-right font-[family-name:var(--font-quicksand)]\">\r\n                          {message.timestamp}\r\n                        </div>\r\n                      )}\r\n                      {message.attachments && message.attachments.length > 0 && (\r\n                        <div className=\"mt-3 space-y-2\">\r\n                          {message.attachments.map((attachment, idx) => (\r\n                            <div key={idx} className=\"bg-yellow-400 rounded-lg p-3 flex items-center gap-3\">\r\n                              <div className=\"text-3xl\">📄</div>\r\n                              <div className=\"flex-grow\">\r\n                                <p className=\"font-semibold text-sm\">{attachment.name}</p>\r\n                                <p className=\"text-xs text-gray-600\">{attachment.size}</p>\r\n                              </div>\r\n                              <div className=\"flex gap-2\">\r\n                                <button className=\"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium\">\r\n                                  View File\r\n                                </button>\r\n                                <button className=\"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium\">\r\n                                  Download File\r\n                                </button>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              )}\r\n              {isLoading && (\r\n                <div className=\"flex justify-start\">\r\n                  <div className=\"bg-[#e8eef5] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)]\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <div className=\"flex space-x-1\">\r\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"></div>\r\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                        <div className=\"w-2 h-2 bg-yellow-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Input Form - Compact spacing */}\r\n          <form onSubmit={handleSubmit} className=\"relative flex-shrink-0\">\r\n            <div className={`rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4 transition-colors duration-300 ${\r\n              isDarkMode\r\n                ? 'bg-gradient-to-r from-[#1e3a5f] via-[#2d4a6e] to-[#3d5a7e]'\r\n                : 'bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a]'\r\n            }`}>\r\n              <input\r\n                value={input}\r\n                onChange={(e) => setInput(e.target.value)}\r\n                placeholder=\"ASK PIP...\"\r\n                disabled={isLoading}\r\n                className=\"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed\"\r\n              />\r\n              <button\r\n                type=\"submit\"\r\n                disabled={isLoading || !input.trim()}\r\n                className=\"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]\"\r\n                aria-label=\"Send message\"\r\n              >\r\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path d=\"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n      \r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n", "'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\n\r\ninterface Message {\r\n  id: string;\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n}\r\n\r\nexport default function ChatPage() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() || isLoading) return;\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      role: 'user',\r\n      content: input,\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    const assistantMessage: Message = {\r\n      id: (Date.now() + 1).toString(),\r\n      role: 'assistant',\r\n      content: '',\r\n    };\r\n\r\n    setMessages(prev => [...prev, assistantMessage]);\r\n\r\n    try {\r\n      const response = await fetch('/api/chat', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          messages: [...messages, userMessage],\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch response');\r\n      }\r\n\r\n      const reader = response.body?.getReader();\r\n      const decoder = new TextDecoder();\r\n\r\n      if (reader) {\r\n        while (true) {\r\n          const { done, value } = await reader.read();\r\n          if (done) break;\r\n\r\n          const chunk = decoder.decode(value);\r\n          const lines = chunk.split('\\n');\r\n\r\n          for (const line of lines) {\r\n            if (line.startsWith('data: ')) {\r\n              const data = line.slice(6);\r\n              if (data === '[DONE]') break;\r\n\r\n              try {\r\n                const parsed = JSON.parse(data);\r\n                if (parsed.content) {\r\n                  setMessages(prev => prev.map(msg =>\r\n                    msg.id === assistantMessage.id\r\n                      ? { ...msg, content: msg.content + parsed.content }\r\n                      : msg\r\n                  ));\r\n                }\r\n              } catch (e) {\r\n                // Ignore parsing errors for non-JSON lines\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error:', error);\r\n      setMessages(prev => prev.map(msg =>\r\n        msg.id === assistantMessage.id\r\n          ? { ...msg, content: 'Sorry, I encountered an error. Please try again.' }\r\n          : msg\r\n      ));\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\" style={{ background: 'linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)' }}>\r\n      <Navbar />\r\n      \r\n      <div className=\"mx-auto w-full max-w-4xl py-8 px-4 mt-24 flex-grow\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-8\">\r\n          <div>\r\n            <h1 className=\"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n              Pusat Informasi Publik\r\n            </h1>\r\n            <h2 className=\"text-2xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]\">\r\n              Fakultas Teknik Universitas Indonesia\r\n            </h2>\r\n          </div>\r\n          <p className=\"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]\">\r\n            AI Assistant untuk Informasi dan Layanan FTUI\r\n          </p>\r\n          {/* Mode Switcher */}\r\n          <div className=\"flex justify-center gap-4 mt-6\">\r\n            <div className=\"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n              💬 Chat Mode\r\n            </div>\r\n            <Link href=\"/rag\">\r\n              <div className=\"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n                📚 RAG Mode\r\n              </div>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Chat Messages */}\r\n        <div ref={chatContainerRef} className=\"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200\">\r\n          <div className=\"p-6 space-y-6\">\r\n            {messages.length === 0 ? (\r\n              <div className=\"text-center text-[#346ad5] mt-8\">\r\n                <div className=\"text-6xl mb-4\">🏛️</div>\r\n                <p className=\"text-lg font-semibold\">Selamat datang di PIP FTUI!</p>\r\n                <p className=\"text-sm\">Silakan ajukan pertanyaan tentang Fakultas Teknik UI.</p>\r\n              </div>\r\n            ) : (\r\n              messages.map((message) => (\r\n                <div\r\n                  key={message.id}\r\n                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n                >\r\n                  <div\r\n                    className={`\r\n                      max-w-[80%] rounded-2xl px-6 py-4 shadow-md\r\n                      ${message.role === 'user'\r\n                        ? 'bg-[#346ad5] text-white'\r\n                        : 'bg-white text-gray-800 border border-gray-200'}\r\n                    `}\r\n                  >\r\n                    <div className={`text-xs mb-2 font-medium ${\r\n                      message.role === 'user' ? 'text-blue-100' : 'text-[#346ad5]'\r\n                    }`}>\r\n                      {message.role === 'user' ? 'You' : 'PIP Assistant'}\r\n                    </div>\r\n                    <div className={`text-sm leading-relaxed whitespace-pre-wrap ${\r\n                      message.role === 'user' ? 'text-white' : 'text-gray-700'\r\n                    }`}>\r\n                      {message.content}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))\r\n            )}\r\n            {isLoading && (\r\n              <div className=\"flex justify-start\">\r\n                <div className=\"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <div className=\"flex space-x-1\">\r\n                      <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\"></div>\r\n                      <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                      <div className=\"w-2 h-2 bg-[#fae664] rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                    </div>\r\n                    <span className=\"text-sm text-[#346ad5]\">PIP Assistant sedang berpikir...</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Input Form */}\r\n        <form onSubmit={handleSubmit} className=\"relative flex-shrink-0\">\r\n          <div className=\"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4\">\r\n            <input\r\n              value={input}\r\n              onChange={(e) => setInput(e.target.value)}\r\n              placeholder=\"ASK PIP...\"\r\n              disabled={isLoading}\r\n              className=\"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed\"\r\n            />\r\n            <button\r\n              type=\"submit\"\r\n              disabled={isLoading || !input.trim()}\r\n              className=\"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]\"\r\n              aria-label=\"Send message\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path d=\"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n      \r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n", "'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\n\r\ninterface Message {\r\n  id: string;\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  sources?: string[];\r\n}\r\n\r\ninterface Document {\r\n  name: string;\r\n}\r\n\r\nexport default function RAGPage() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [documents, setDocuments] = useState<Document[]>([]);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [showUpload, setShowUpload] = useState(false);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  useEffect(() => {\r\n    fetchDocuments();\r\n  }, []);\r\n\r\n  const fetchDocuments = async () => {\r\n    try {\r\n      const response = await fetch('/api/documents');\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setDocuments(data.documents.map((name: string) => ({ name })));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching documents:', error);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    setIsUploading(true);\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    try {\r\n      const response = await fetch('/api/documents', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        await fetchDocuments();\r\n        alert('Document uploaded successfully!');\r\n        setShowUpload(false);\r\n      } else {\r\n        const error = await response.json();\r\n        alert(`Upload failed: ${error.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error uploading file:', error);\r\n      alert('Failed to upload document');\r\n    } finally {\r\n      setIsUploading(false);\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = '';\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() || isLoading) return;\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      role: 'user',\r\n      content: input,\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await fetch('/api/rag', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          question: input,\r\n          top_k: 3,\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch response');\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      const assistantMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: data.answer,\r\n        sources: data.sources,\r\n      };\r\n\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n    } catch (error) {\r\n      console.error('Error:', error);\r\n      const errorMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: 'Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running.',\r\n      };\r\n      setMessages(prev => [...prev, errorMessage]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\" style={{ background: 'linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)' }}>\r\n      <Navbar />\r\n      \r\n      <div className=\"mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-8\">\r\n          <div>\r\n            <h1 className=\"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n              PIP FTUI - RAG Mode\r\n            </h1>\r\n            <h2 className=\"text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]\">\r\n              Retrieval-Augmented Generation\r\n            </h2>\r\n          </div>\r\n          <p className=\"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]\">\r\n            Cari informasi FTUI berdasarkan dokumen yang tersedia\r\n          </p>\r\n          {/* Mode Switcher */}\r\n          <div className=\"flex justify-center gap-4 mt-6\">\r\n            <Link href=\"/chat\">\r\n              <div className=\"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n                💬 Chat Mode\r\n              </div>\r\n            </Link>\r\n            <div className=\"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n              📚 RAG Mode\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex gap-6\">\r\n          {/* Sidebar - Documents */}\r\n          <div className=\"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h2 className=\"text-xl font-bold text-gray-800\">Documents</h2>\r\n              <button\r\n                onClick={() => setShowUpload(!showUpload)}\r\n                className=\"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\r\n              >\r\n                {showUpload ? 'Cancel' : '+ Upload'}\r\n              </button>\r\n            </div>\r\n\r\n            {showUpload && (\r\n              <div className=\"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300\">\r\n                <input\r\n                  ref={fileInputRef}\r\n                  type=\"file\"\r\n                  onChange={handleFileUpload}\r\n                  disabled={isUploading}\r\n                  accept=\".pdf,.txt,.docx,.md,.html\"\r\n                  className=\"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer\"\r\n                />\r\n                {isUploading && (\r\n                  <p className=\"text-sm text-purple-600 mt-2\">Uploading...</p>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"overflow-y-auto h-[calc(100%-100px)]\">\r\n              {documents.length === 0 ? (\r\n                <div className=\"text-center text-gray-500 mt-8\">\r\n                  <div className=\"text-4xl mb-2\">📄</div>\r\n                  <p className=\"text-sm\">No documents yet</p>\r\n                  <p className=\"text-xs text-gray-400 mt-1\">Upload a document to start</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  {documents.map((doc, idx) => (\r\n                    <div\r\n                      key={idx}\r\n                      className=\"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors\"\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <span className=\"text-2xl\">📄</span>\r\n                        <span className=\"text-sm text-gray-700 truncate flex-1\">\r\n                          {doc.name}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Main Chat Area */}\r\n          <div className=\"flex-1\">\r\n            {/* Chat Messages */}\r\n            <div ref={chatContainerRef} className=\"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200\">\r\n              <div className=\"p-6 space-y-6\">\r\n                {messages.length === 0 ? (\r\n                  <div className=\"text-center text-[#346ad5] mt-8\">\r\n                    <div className=\"text-6xl mb-4\">📚</div>\r\n                    <p className=\"text-lg font-semibold\">Tanyakan tentang informasi FTUI</p>\r\n                    <p className=\"text-sm\">Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan.</p>\r\n                  </div>\r\n                ) : (\r\n                  messages.map((message) => (\r\n                    <div\r\n                      key={message.id}\r\n                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n                    >\r\n                      <div\r\n                        className={`\r\n                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md\r\n                          ${message.role === 'user'\r\n                            ? 'bg-[#346ad5] text-white'\r\n                            : 'bg-white text-gray-800 border border-gray-200'}\r\n                        `}\r\n                      >\r\n                        <div className={`text-xs mb-2 font-medium ${\r\n                          message.role === 'user' ? 'text-blue-100' : 'text-[#346ad5]'\r\n                        }`}>\r\n                          {message.role === 'user' ? 'You' : 'PIP RAG Assistant'}\r\n                        </div>\r\n                        <div className={`text-sm leading-relaxed whitespace-pre-wrap ${\r\n                          message.role === 'user' ? 'text-white' : 'text-gray-700'\r\n                        }`}>\r\n                          {message.content}\r\n                        </div>\r\n                        {message.sources && message.sources.length > 0 && (\r\n                          <div className=\"mt-4 pt-4 border-t border-gray-300\">\r\n                            <p className=\"text-xs font-semibold text-gray-600 mb-2\">Sources:</p>\r\n                            <div className=\"space-y-2\">\r\n                              {message.sources.map((source, idx) => (\r\n                                <div key={idx} className=\"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200\">\r\n                                  {source}\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                )}\r\n                {isLoading && (\r\n                  <div className=\"flex justify-start\">\r\n                    <div className=\"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200\">\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <div className=\"flex space-x-1\">\r\n                          <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\"></div>\r\n                          <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                          <div className=\"w-2 h-2 bg-[#fae664] rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                        </div>\r\n                        <span className=\"text-sm text-[#346ad5]\">Mencari di dokumen...</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Input Form */}\r\n            <form onSubmit={handleSubmit} className=\"relative flex-shrink-0\">\r\n              <div className=\"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4\">\r\n                <input\r\n                  value={input}\r\n                  onChange={(e) => setInput(e.target.value)}\r\n                  placeholder=\"ASK PIP...\"\r\n                  disabled={isLoading || documents.length === 0}\r\n                  className=\"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed\"\r\n                />\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={isLoading || !input.trim() || documents.length === 0}\r\n                  className=\"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]\"\r\n                  aria-label=\"Send message\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n", "'use client';\r\n\r\nimport Image from 'next/image';\r\nimport { useState } from 'react';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\nexport default function ContactPage() {\r\n  const { isDarkMode } = useTheme();\r\n  const [formData, setFormData] = useState({\r\n    nama: '',\r\n    email: '',\r\n    subject: '',\r\n    pesan: '',\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    // Handle form submission\r\n    console.log('Form submitted:', formData);\r\n    alert('Pesan Anda telah terkirim!');\r\n    setFormData({ nama: '', email: '', subject: '', pesan: '' });\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col relative overflow-x-hidden\">\r\n      {/* Background with Yellow Circles */}\r\n      <div className=\"fixed inset-0 w-full h-full -z-10\">\r\n        {/* White background layer for light mode */}\r\n        <div className={`absolute inset-0 transition-colors duration-300 ${\r\n          isDarkMode ? 'bg-transparent' : 'bg-white'\r\n        }`}></div>\r\n        {/* SVG overlay with brightness control */}\r\n        <div className={`absolute inset-0 transition-all duration-300 ${\r\n          isDarkMode ? 'brightness-[0.4]' : ''\r\n        }`}>\r\n          <Image\r\n            src=\"/Home_Page/Backround Design.svg\"\r\n            alt=\"Background with yellow circles\"\r\n            fill\r\n            className=\"object-cover\"\r\n            priority\r\n            quality={100}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <Navbar />\r\n\r\n      <div className=\"flex-grow pt-24 pb-8 px-8\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          {/* Faculty and Department Contact Section */}\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\r\n            {/* Faculty Contact Info */}\r\n            <div className={`rounded-3xl p-8 text-white shadow-2xl transition-colors duration-300 ${\r\n              isDarkMode\r\n                ? 'bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]'\r\n                : 'bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]'\r\n            }`}>\r\n              <h2 className=\"text-2xl font-bold mb-6 font-[family-name:var(--font-comfortaa)]\">\r\n                Faculty Contact Info\r\n              </h2>\r\n\r\n              {/* Head Office */}\r\n              <div className=\"mb-6\">\r\n                <h3 className=\"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n                  Head Office\r\n                </h3>\r\n                <p className=\"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]\">\r\n                  Gedung Dekanat FTUI Lt. 2<br />\r\n                  Fakultas Teknik Universitas Indonesia<br />\r\n                  Kampus UI Depok<br />\r\n                  +6221 7863504, +6221 7863505\r\n                </p>\r\n              </div>\r\n\r\n              {/* Kantor Humas dan Protokol */}\r\n              <div className=\"mb-6\">\r\n                <h3 className=\"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n                  Kantor Humas dan Protokol\r\n                </h3>\r\n                <p className=\"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]\">\r\n                  Gedung GK IPAJI lantai 1<br />\r\n                  Fakultas Teknik Universitas Indonesia<br />\r\n                  Kampus UI Depok<br />\r\n                  +6221 78888430 ext 106<br />\r\n                  <EMAIL>\r\n                </p>\r\n              </div>\r\n\r\n              {/* Operating Hours */}\r\n              <div className=\"mb-6\">\r\n                <p className=\"text-sm font-bold font-[family-name:var(--font-quicksand)]\">\r\n                  Mon – Fri 8:00A.M. – 4:00P.M.\r\n                </p>\r\n                <p className=\"text-sm font-semibold font-[family-name:var(--font-quicksand)]\">\r\n                  Social Info\r\n                </p>\r\n              </div>\r\n\r\n              {/* Social Media Icons */}\r\n              <div className=\"flex gap-4\">\r\n                <a href=\"https://instagram.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/instagram 1.png\" alt=\"Instagram\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n                <a href=\"https://linkedin.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/linkedin 1.png\" alt=\"LinkedIn\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n                <a href=\"https://youtube.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/youtube 1.png\" alt=\"YouTube\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n                <a href=\"https://facebook.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/facebook 1.png\" alt=\"Facebook\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Contact Form */}\r\n            <div className={`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${\r\n              isDarkMode\r\n                ? 'bg-gradient-to-br from-[#2d4a6e] to-[#1e3a5f]'\r\n                : 'bg-gradient-to-br from-[#7a8a9a] to-[#5a7a9d]'\r\n            }`}>\r\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Nama\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"nama\"\r\n                    value={formData.nama}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Masukkan nama Anda\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Email\r\n                  </label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Masukkan email Anda\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Subject\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"subject\"\r\n                    value={formData.subject}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Masukkan subjek\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Pesan\r\n                  </label>\r\n                  <textarea\r\n                    name=\"pesan\"\r\n                    value={formData.pesan}\r\n                    onChange={handleChange}\r\n                    required\r\n                    rows={6}\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] resize-none font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Tulis pesan Anda\"\r\n                  />\r\n                </div>\r\n\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 font-bold py-3 rounded-xl transition-all shadow-lg font-[family-name:var(--font-comfortaa)]\"\r\n                >\r\n                  Kirim Pesan\r\n                </button>\r\n              </form>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Department Contact Info */}\r\n          <div className={`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${\r\n            isDarkMode\r\n              ? 'bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]'\r\n              : 'bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]'\r\n          }`}>\r\n            <h2 className=\"text-2xl font-bold mb-6 text-white font-[family-name:var(--font-comfortaa)]\">\r\n              Departement Contact Info\r\n            </h2>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n              {/* Department Cards */}\r\n              {[\r\n                {\r\n                  name: 'Departemen Teknik Sipil',\r\n                  email: '<EMAIL>, <EMAIL>',\r\n                  telp: '+6221 7270029 - 7270028',\r\n                  whatsapp: '082211135202 (Sekretariat DTS)',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Sipil',\r\n                  email: '<EMAIL>',\r\n                  telp: 'WA 7270042 - 78849042',\r\n                  whatsapp: 'WA 081212776702 (sekretariat Teknik Mesin)',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Sipil',\r\n                  email: '<EMAIL>, <EMAIL>',\r\n                  telp: '+6221 7270029 - 7270028',\r\n                  whatsapp: '082211135202 (Sekretariat DTS)',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Elektro',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7270078 - 7863504',\r\n                  whatsapp: '081289606440',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Metalurgi',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 78849044',\r\n                  whatsapp: '081519996009',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Kimia',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7863516',\r\n                  whatsapp: '082112025025',\r\n                },\r\n                {\r\n                  name: 'Departemen Arsitektur',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7270062',\r\n                  whatsapp: '081296661126',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Industri',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7270041',\r\n                  whatsapp: '082112345678',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Komputer',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7863512',\r\n                  whatsapp: '081234567890',\r\n                },\r\n              ].map((dept, idx) => (\r\n                <div\r\n                  key={idx}\r\n                  className=\"bg-white/90 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl transition-shadow\"\r\n                >\r\n                  <h3 className=\"text-[#2d5a9e] font-bold text-sm mb-3 font-[family-name:var(--font-comfortaa)]\">\r\n                    {dept.name}\r\n                  </h3>\r\n                  <div className=\"space-y-2 text-xs font-[family-name:var(--font-quicksand)]\">\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-700\">Email</p>\r\n                      <p className=\"text-gray-600\">{dept.email}</p>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-700\">Telp</p>\r\n                      <p className=\"text-gray-600\">{dept.telp}</p>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-700\">Whatsapp Only</p>\r\n                      <p className=\"text-gray-600\">{dept.whatsapp}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n", "'use client';\r\n\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport default function LandingPage() {\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\r\n      {/* Background Image with Overlay */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        <Image \r\n          src=\"/Landing_Page/Background.png\"\r\n          alt=\"FTUI Campus Background\"\r\n          fill\r\n          className=\"object-cover\"\r\n          priority\r\n          quality={100}\r\n        />\r\n        {/* Blue overlay to match the image */}\r\n        <div className=\"absolute inset-0 bg-[#4a6b8a]/70\"></div>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <div className=\"relative z-10 text-center px-4 flex flex-col items-center\">\r\n        {/* Logo Container */}\r\n        <div className=\"mb-8 flex justify-center\">\r\n          <div className=\"relative\">\r\n            <Image \r\n              src=\"/Landing_Page/PIP LOGO 2.svg\"\r\n              alt=\"PIP FTUI Logo\"\r\n              width={700}\r\n              height={250}\r\n              className=\"drop-shadow-2xl w-full max-w-[700px] h-auto\"\r\n              priority\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Dive In Button */}\r\n        <Link href=\"/home\">\r\n          <div className=\"inline-block relative group cursor-pointer mt-4\">\r\n            <div className=\"relative w-[180px] h-[65px]\">\r\n              <Image \r\n                src=\"/Landing_Page/Button.svg\"\r\n                alt=\"Dive In Button\"\r\n                width={180}\r\n                height={65}\r\n                className=\"transition-transform duration-300 group-hover:scale-105 drop-shadow-lg\"\r\n              />\r\n              {/* Dive In Text Overlay */}\r\n              <div className=\"absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none\">\r\n                <span className=\"text-[#ffffff] text-[22px] font-bold font-[family-name:var(--font-comfortaa)] tracking-wide drop-shadow-sm\">\r\n                  Dive In\r\n                </span>\r\n\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM", "AppRouterContext", "ReactServerDOMTurbopackClient", "HooksClientContext", "ServerInsertedHtml", "HandleISRError", "workAsyncStorage", "window", "undefined", "error", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p"], "mappings": "gjBAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEC,QAAQ,8BCFjCJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACE,gBAAgB,+BCFvCL,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEG,6BAA6B,8BCFtDN,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACI,kBAAkB,+BCFzCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACK,kBAAkB,wGCQzBC,iBAAAA,qCAAAA,KAVhB,IAAMC,EAGER,EAAQ,CAAA,CAAA,IAAA,GACRQ,MAHN,OAAOC,GAGe,CAMjB,EALDC,KAJc,EASJH,EAAe,CAAyB,EAAzB,GAAA,OAAEI,CAAK,CAAkB,CAAzB,EAC7B,GAAIH,EAAkB,CACpB,IAAMI,EAAQJ,EAAiBK,QAAQ,GACvC,GAAID,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOE,YAAAA,AAAY,IAAIF,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAOG,kBAAAA,AAAkB,EAElD,CAFoD,KACpDC,QAAQL,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,+TCgCA,OADA,AADA,GAEA,qCAAA,GAD2C,uBAjDZ,CAAA,CAAA,IAAA,GAEzBM,EAAS,CACbN,EA6C8E,IA7CvE,CAELO,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA8BA,EAzBA,SAASC,AAAmB,AAyBbA,CAzBsC,EAAzB,GAAA,OAAEnB,CAAK,CAAkB,CAAzB,EACpBoB,EAA6BpB,MAAAA,EAAAA,KAAAA,EAAAA,EAAOoB,MAAM,CAChD,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAACC,CADH,MACGA,CAAKC,GAAG,2BACP,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAAA,GACD,GAAA,EAAA,IAAA,EAACC,OAAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC5B,EAAAA,cAAc,CAAA,CAACI,MAAOA,IACvB,CAAA,EAAA,EAAA,GAAA,EAACyB,MAAAA,CAAIC,MAAOpB,EAAON,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACyB,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,IAAA,EAACE,KAAAA,CAAGD,MAAOpB,EAAOQ,IAAI,WAAE,wBACAM,EAAS,SAAW,SAAS,8CACvBtB,OAAO8B,QAAQ,CAACC,QAAQ,CAAC,YAAU,IAC9DT,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAS,CAAA,EAAA,EAAA,EAATA,CAAS,EAACU,IAAAA,CAAEJ,GAAZN,GAAmBd,EAAOQ,IAAI,UAAI,WAAUM,IAAgB,eAMzE,yRC9CA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAce,SAAS,IACtB,GAAM,YAAE,CAAU,CAAE,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,IACzB,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAoB,EAAE,EAChD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACpB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAC9C,IAAM,EAAmB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAQhD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KALJ,EAAiB,OAAO,EAAE,CAC5B,EAAiB,OAAO,CAAC,SAAS,CAAG,EAAiB,OAAO,CAAC,YAAA,AAAY,CAM9E,EAAG,CAAC,EAAS,EAEb,IAAM,EAAe,MAAO,IAE1B,GADA,EAAE,cAAc,GACZ,CAAC,EAAM,IAAI,IAAM,EAAW,OAEhC,IAAM,EAAuB,CAC3B,GAAI,KAAK,GAAG,GAAG,QAAQ,GACvB,KAAM,OACN,QAAS,EACT,UAAW,IAAI,OAAO,kBAAkB,CAAC,QAAS,CAAE,KAAM,UAAW,OAAQ,SAAU,EACzF,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAY,EAC1C,EAAS,IACT,GAAa,GAGb,WAAW,KACT,IAAM,EAA4B,CAChC,GAAI,CAAC,KAAK,GAAG,IAAK,CAAC,CAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,0GACT,UAAW,IAAI,OAAO,kBAAkB,CAAC,QAAS,CAAE,KAAM,UAAW,OAAQ,SAAU,EACzF,EACA,EAAY,GAAQ,IAAI,EAAM,EAAiB,EAC/C,GAAa,EACf,EAAG,IACL,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kEAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,gDAAgD,EAC/D,EAAa,iBAAmB,WAAA,CAChC,GAEF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,6CAA6C,EAC5D,EAAa,mBAAqB,GAAA,CAClC,UACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,kCACJ,IAAI,iCACJ,IAAI,CAAA,CAAA,EACJ,UAAU,eACV,QAAQ,CAAA,CAAA,EACR,QAAS,WAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,GAEP,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0CAA0C,MAAO,CAAE,SAAU,oBAAqB,UAAW,oBAAqB,YAE/H,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,8HAA8H,EAC7I,EACI,uEACA,uEAAA,CACJ,UACA,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,IAAK,EAAkB,UAAU,8DACf,IAApB,EAAS,MAAM,CACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4DAAmD,oBAIpE,EAAS,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAW,CAAC,KAAK,EAAmB,SAAjB,EAAQ,IAAI,CAAc,cAAgB,gBAAA,CAAiB,UAE9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAW,CAAC,0EAA0E,EACpF,AAAiB,WAAT,IAAI,CACR,6BACA,6BAAA,CACJ,WAEF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oGACZ,EAAQ,OAAO,GAEjB,EAAQ,SAAS,EAChB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8FACZ,EAAQ,SAAS,GAGrB,EAAQ,WAAW,EAAI,EAAQ,WAAW,CAAC,MAAM,CAAG,GACnD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0BACZ,EAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,EAAY,IACpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAc,UAAU,iEACvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oBAAW,OAC1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAyB,EAAW,IAAI,GACrD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAyB,EAAW,IAAI,MAEvD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,8FAAqF,cAGvG,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,8FAAqF,uBAVjG,UArBb,EAAQ,EAAE,GA2CpB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAkD,MAAO,CAAE,eAAgB,MAAO,IACjG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDAAoD,MAAO,CAAE,eAAgB,MAAO,kBAUjH,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAU,EAAc,UAAU,kCACtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,6GAA6G,EAC5H,EACI,6DACA,6DAAA,CACJ,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,YAAY,aACZ,SAAU,EACV,UAAU,iKAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAa,CAAC,EAAM,IAAI,GAClC,UAAU,wNACV,aAAW,wBAEX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,eAAe,QAAQ,qBACnD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,wDAQpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,KAGb,kECvMA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAQe,SAAS,IACtB,GAAM,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAoB,EAAE,EAChD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,EAAmB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAQhD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KALJ,EAAiB,OAAO,EAAE,AAC5B,GAAiB,OAAO,CAAC,SAAS,CAAG,EAAiB,OAAO,CAAC,YAAY,AAAZ,CAMlE,EAAG,CAAC,EAAS,EAEb,IAAM,EAAe,MAAO,IAE1B,GADA,EAAE,cAAc,GACZ,CAAC,EAAM,IAAI,IAAM,EAAW,OAEhC,IAAM,EAAuB,CAC3B,GAAI,KAAK,GAAG,GAAG,QAAQ,GACvB,KAAM,OACN,QAAS,CACX,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAY,EAC1C,EAAS,IACT,GAAa,GAEb,IAAM,EAA4B,CAChC,GAAI,AAAC,MAAK,GAAG,GAAK,CAAC,EAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,EACX,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAiB,EAE/C,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,YAAa,CACxC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,IAAI,EAAU,EAAY,AACtC,EACF,GAEA,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,4BAGlB,IAAM,EAAS,EAAS,IAAI,EAAE,YACxB,EAAU,IAAI,YAEpB,GAAI,EACF,MADU,AACH,CAAM,CACX,GAAM,MAAE,CAAI,OAAE,CAAK,CAAE,CAAG,MAAM,EAAO,IAAI,GACzC,GAAI,EAAM,MAKV,IAAK,IAAM,KAHG,AACA,EADQ,CAGH,KAHS,CAAC,AAGH,GAFN,KAAK,CAAC,MAGxB,GAAI,EAAK,UAAU,CAAC,UAAW,CAC7B,IAAM,EAAO,EAAK,KAAK,CAAC,GACxB,GAAa,WAAT,EAAmB,MAEvB,GAAI,CACF,IAAM,EAAS,KAAK,KAAK,CAAC,GACtB,EAAO,OAAO,EAChB,AADkB,EACN,GAAQ,EAAK,GAAG,CAAC,GAC3B,EAAI,EAAE,GAAK,EAAiB,EAAE,CAC1B,CAAE,GAAG,CAAG,CAAE,QAAS,EAAI,OAAO,CAAG,EAAO,OAAO,AAAC,EAChD,GAGV,CAAE,MAAO,EAAG,CAEZ,CACF,CAEJ,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,SAAU,GACxB,EAAY,GAAQ,EAAK,GAAG,CAAC,GAC3B,EAAI,EAAE,GAAK,EAAiB,EAAE,CAC1B,CAAE,GAAG,CAAG,CAAE,QAAS,kDAAmD,EACtE,GAER,QAAU,CACR,GAAa,EACf,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BAA6B,MAAO,CAAE,WAAY,gEAAiE,YAChI,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,GAEP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uFAA8E,2BAG5F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2FAAkF,6CAIlG,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oFAA2E,kDAIxF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uHAA8G,iBAG7H,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,gBACT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+KAAsK,wBAQ3L,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,IAAK,EAAkB,UAAU,gGACpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACQ,IAApB,EAAS,MAAM,CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,QAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,gCACrC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,6DAGzB,EAAS,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAW,CAAC,KAAK,EAAmB,SAAjB,EAAQ,IAAI,CAAc,cAAgB,gBAAA,CAAiB,UAE9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAW,CAAC;;sBAEV,EAAmB,SAAjB,EAAQ,IAAI,CACV,0BACA,gDAAgD;oBACtD,CAAC,WAED,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,yBAAyB,EACvB,SAAjB,EAAQ,IAAI,CAAc,gBAAkB,iBAAA,CAC5C,UACkB,SAAjB,EAAQ,IAAI,CAAc,MAAQ,kBAErC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,4CAA4C,EAC1C,SAAjB,EAAQ,IAAI,CAAc,aAAe,gBAAA,CACzC,UACC,EAAQ,OAAO,OAnBf,EAAQ,EAAE,GAyBpB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAAmD,MAAO,CAAE,eAAgB,MAAO,IAClG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAAmD,MAAO,CAAE,eAAgB,MAAO,OAEpG,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,kCAAyB,iDASrD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAU,EAAc,UAAU,kCACtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qJACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,YAAY,aACZ,SAAU,EACV,UAAU,iKAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAa,CAAC,EAAM,IAAI,GAClC,UAAU,wNACV,aAAW,wBAEX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,eAAe,QAAQ,qBACnD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,sDAOlB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,KAGb,iEC7NA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAae,SAAS,IACtB,GAAM,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAa,EAAE,EACnD,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACzC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACvC,EAAmB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAC1C,EAAe,CAAA,EAAA,EAAA,MAAA,AAAM,EAAmB,MAQ9C,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KALJ,EAAiB,OAAO,EAAE,CAC5B,EAAiB,OAAO,CAAC,SAAS,CAAG,EAAiB,OAAO,CAAC,YAAY,AAAZ,CAMlE,EAAG,CAAC,EAAS,EAEb,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,GACF,EAAG,EAAE,EAEL,IAAM,EAAiB,UACrB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,kBAC7B,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAa,EAAK,SAAS,CAAC,GAAG,CAAE,AAAD,IAAmB,GAAD,GAAG,EAAK,CAAC,EAC7D,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,4BAA6B,EAC7C,CACF,EAEM,EAAmB,MAAO,IAC9B,IAAM,EAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAChC,GAAI,CAAC,EAAM,OAEX,EAAe,IACf,IAAM,EAAW,IAAI,SACrB,EAAS,MAAM,CAAC,OAAQ,GAExB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,iBAAkB,CAC7C,OAAQ,OACR,KAAM,CACR,GAEA,GAAI,EAAS,EAAE,CACb,CADe,KACT,IACN,MAAM,mCACN,GAAc,OACT,CACL,IAAM,EAAQ,MAAM,EAAS,IAAI,GACjC,MAAM,CAAC,eAAe,EAAE,EAAM,KAAK,CAAA,CAAE,CACvC,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,wBAAyB,GACvC,MAAM,4BACR,QAAU,CACR,GAAe,GACX,EAAa,OAAO,EAAE,CACxB,EAAa,OAAO,CAAC,KAAK,CAAG,EAAA,CAEjC,CACF,EAEM,EAAe,MAAO,IAE1B,GADA,EAAE,cAAc,GACZ,CAAC,EAAM,IAAI,IAAM,EAAW,OAEhC,IAAM,EAAuB,CAC3B,GAAI,KAAK,GAAG,GAAG,QAAQ,GACvB,KAAM,OACN,QAAS,CACX,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAY,EAC1C,EAAS,IACT,GAAa,GAEb,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,WAAY,CACvC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,EACV,MAAO,CACT,EACF,GAEA,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,4BAGlB,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAA4B,CAChC,GAAI,CAAC,KAAK,GAAG,IAAK,CAAC,CAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,EAAK,MAAM,CACpB,QAAS,EAAK,OAAO,AACvB,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAiB,CACjD,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,SAAU,GACxB,IAAM,EAAwB,CAC5B,GAAI,CAAC,KAAK,GAAG,IAAK,CAAC,CAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,yGACX,EACA,EAAY,GAAQ,IAAI,EAAM,EAAa,CAC7C,QAAU,CACR,GAAa,EACf,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BAA6B,MAAO,CAAE,WAAY,gEAAiE,YAChI,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,GAEP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uFAA8E,wBAG5F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,sCAIjG,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oFAA2E,0DAIxF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,iBACT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+KAAsK,mBAIvL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uHAA8G,sBAMjI,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qFACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2CAAkC,cAChD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAc,CAAC,GAC9B,UAAU,mHAET,EAAa,SAAW,gBAI5B,GACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sFACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,IAAK,EACL,KAAK,OACL,SAAU,EACV,SAAU,EACV,OAAO,4BACP,UAAU,6MAEX,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,wCAA+B,oBAKlD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gDACS,IAArB,EAAU,MAAM,CACf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,qBACvB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,kCAG5C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAU,GAAG,CAAC,CAAC,EAAK,IACnB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAU,gGAEV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oBAAW,OAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iDACb,EAAI,IAAI,OANR,WAiBjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mBAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,IAAK,EAAkB,UAAU,gGACpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACQ,IAApB,EAAS,MAAM,CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,oCACrC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,mEAGzB,EAAS,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAW,CAAC,KAAK,EAAE,AAAiB,WAAT,IAAI,CAAc,cAAgB,gBAAA,CAAiB,UAE9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAW,CAAC;;0BAEV,EAAmB,SAAjB,EAAQ,IAAI,CACV,0BACA,gDAAgD;wBACtD,CAAC,WAED,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,yBAAyB,EACvB,SAAjB,EAAQ,IAAI,CAAc,gBAAkB,iBAAA,CAC5C,UACkB,SAAjB,EAAQ,IAAI,CAAc,MAAQ,sBAErC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,4CAA4C,EAC1C,SAAjB,EAAQ,IAAI,CAAc,aAAe,gBAAA,CACzC,UACC,EAAQ,OAAO,GAEjB,EAAQ,OAAO,EAAI,EAAQ,OAAO,CAAC,MAAM,CAAG,GAC3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+CACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oDAA2C,aACxD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,EAAQ,IAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAc,UAAU,6EACtB,GADO,aA1Bf,EAAQ,EAAE,GAqCpB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAAmD,MAAO,CAAE,eAAgB,MAAO,IAClG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDAAmD,MAAO,CAAE,eAAgB,MAAO,OAEpG,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,kCAAyB,sCASrD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,SAAU,EAAc,UAAU,kCACtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qJACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,YAAY,aACZ,SAAU,GAAa,AAAqB,MAAX,MAAM,CACvC,UAAU,iKAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAa,CAAC,EAAM,IAAI,IAA2B,IAArB,EAAU,MAAM,CACxD,UAAU,wNACV,aAAW,wBAEX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,eAAe,QAAQ,qBACnD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,4DAStB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,KAGb,kECnUA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAEe,SAAS,IACtB,GAAM,YAAE,CAAU,CAAE,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,IACzB,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,CACvC,KAAM,GACN,MAAO,GACP,QAAS,GACT,MAAO,EACT,GAUM,EAAe,AAAC,IACpB,EAAY,CACV,GAAG,CAAQ,CACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAE,EAAE,MAAM,CAAC,KAAK,AACjC,EACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kEAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,gDAAgD,EAC/D,EAAa,iBAAmB,WAAA,CAChC,GAEF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,6CAA6C,EAC5D,EAAa,mBAAqB,GAAA,CAClC,UACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,kCACJ,IAAI,iCACJ,IAAI,CAAA,CAAA,EACJ,UAAU,eACV,QAAQ,CAAA,CAAA,EACR,QAAS,WAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,GAEP,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,qEAAqE,EACpF,EACI,gDACA,gDAAA,CACJ,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4EAAmE,yBAKjF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2EAAkE,gBAGhF,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,6EAAmE,4BACrD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,wCACM,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,kBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,qCAMzB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,2EAAkE,8BAGhF,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,6EAAmE,2BACtD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,wCACO,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,kBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,yBACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAA,GAAK,iCAMhC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sEAA6D,kCAG1E,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,0EAAiE,mBAMhF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,wBAAwB,OAAO,SAAS,IAAI,sBAAsB,UAAU,+CAClF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CAAC,IAAI,0BAA0B,IAAI,YAAY,IAAI,CAAA,CAAA,EAAC,UAAU,uBAGxE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,uBAAuB,OAAO,SAAS,IAAI,sBAAsB,UAAU,+CACjF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CAAC,IAAI,yBAAyB,IAAI,WAAW,IAAI,CAAA,CAAA,EAAC,UAAU,uBAGtE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,sBAAsB,OAAO,SAAS,IAAI,sBAAsB,UAAU,+CAChF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CAAC,IAAI,wBAAwB,IAAI,UAAU,IAAI,CAAA,CAAA,EAAC,UAAU,uBAGpE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,uBAAuB,OAAO,SAAS,IAAI,sBAAsB,UAAU,+CACjF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CAAC,IAAI,yBAAyB,IAAI,WAAW,IAAI,CAAA,CAAA,EAAC,UAAU,6BAO1E,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,0DAA0D,EACzE,EACI,gDACA,gDAAA,CACJ,UACA,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SA1HI,AAAD,CA0HO,GAzH1B,EAAE,cAAc,GAEhB,QAAQ,GAAG,CAAC,kBAAmB,GAC/B,MAAM,8BACN,EAAY,CAAE,KAAM,GAAI,MAAO,GAAI,QAAS,GAAI,MAAO,EAAG,EAC5D,EAoH0C,UAAU,sBACtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,gGAAuF,SAGxG,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,KAAK,OACL,MAAO,EAAS,IAAI,CACpB,SAAU,EACV,QAAQ,CAAA,CAAA,EACR,UAAU,4LACV,YAAY,0BAIhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,gGAAuF,UAGxG,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,QACL,KAAK,QACL,MAAO,EAAS,KAAK,CACrB,SAAU,EACV,QAAQ,CAAA,CAAA,EACR,UAAU,4LACV,YAAY,2BAIhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,gGAAuF,YAGxG,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,KAAK,UACL,MAAO,EAAS,OAAO,CACvB,SAAU,EACV,QAAQ,CAAA,CAAA,EACR,UAAU,4LACV,YAAY,uBAIhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,gGAAuF,UAGxG,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,KAAK,QACL,MAAO,EAAS,KAAK,CACrB,SAAU,EACV,QAAQ,CAAA,CAAA,EACR,KAAM,EACN,UAAU,wMACV,YAAY,wBAIhB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,UAAU,4JACX,wBAQP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAW,CAAC,0DAA0D,EACzE,EACI,gDACA,gDAAA,CACJ,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uFAA8E,6BAI5F,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gEAEZ,CACC,CACE,KAAM,0BACN,MAAO,qCACP,KAAM,0BACN,SAAU,gCACZ,EACA,CACE,KAAM,0BACN,MAAO,0BACP,KAAM,wBACN,SAAU,4CACZ,EACA,CACE,KAAM,0BACN,MAAO,qCACP,KAAM,0BACN,SAAU,gCACZ,EACA,CACE,KAAM,4BACN,MAAO,0BACP,KAAM,0BACN,SAAU,cACZ,EACA,CACE,KAAM,8BACN,MAAO,2BACP,KAAM,iBACN,SAAU,cACZ,EACA,CACE,KAAM,0BACN,MAAO,2BACP,KAAM,gBACN,SAAU,cACZ,EACA,CACE,KAAM,wBACN,MAAO,mBACP,KAAM,gBACN,SAAU,cACZ,EACA,CACE,KAAM,6BACN,MAAO,0BACP,KAAM,gBACN,SAAU,cACZ,EACA,CACE,KAAM,6BACN,MAAO,0BACP,KAAM,gBACN,SAAU,cACZ,EACD,CAAC,GAAG,CAAC,CAAC,EAAM,IACX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAEC,UAAU,qGAEV,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FACX,EAAK,IAAI,GAEZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uCAA8B,UAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAK,KAAK,MAE1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uCAA8B,SAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAK,IAAI,MAEzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uCAA8B,kBAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAK,QAAQ,WAjB1C,cA2BjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAM,CAAA,CAAA,KAGb,kECnTA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mFAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,+BACJ,IAAI,yBACJ,IAAI,CAAA,CAAA,EACJ,UAAU,eACV,QAAQ,CAAA,CAAA,EACR,QAAS,MAGX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wCAIjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sEAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,+BACJ,IAAI,gBACJ,MAAO,IACP,OAAQ,IACR,UAAU,8CACV,QAAQ,CAAA,CAAA,QAMd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,iBACT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,2BACJ,IAAI,iBACJ,MAAO,IACP,OAAQ,GACR,UAAU,2EAGZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oGACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,sHAA6G,0BAW7I", "ignoreList": [0, 1, 2, 3, 4, 5, 6]}