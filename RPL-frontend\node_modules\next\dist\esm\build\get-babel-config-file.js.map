{"version": 3, "sources": ["../../src/build/get-babel-config-file.ts"], "sourcesContent": ["import { join } from 'path'\nimport { existsSync } from 'fs'\n\nconst BABEL_CONFIG_FILES = [\n  '.babelrc',\n  '.babelrc.json',\n  '.babelrc.js',\n  '.babelrc.mjs',\n  '.babelrc.cjs',\n  'babel.config.js',\n  'babel.config.json',\n  'babel.config.mjs',\n  'babel.config.cjs',\n]\n\nexport function getBabelConfigFile(dir: string): string | undefined {\n  for (const filename of BABEL_CONFIG_FILES) {\n    const configFilePath = join(dir, filename)\n    const exists = existsSync(configFilePath)\n    if (!exists) {\n      continue\n    }\n    return configFilePath\n  }\n}\n"], "names": ["join", "existsSync", "BABEL_CONFIG_FILES", "getBabelConfigFile", "dir", "filename", "config<PERSON><PERSON><PERSON><PERSON>", "exists"], "mappings": "AAAA,SAASA,IAAI,QAAQ,OAAM;AAC3B,SAASC,UAAU,QAAQ,KAAI;AAE/B,MAAMC,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,SAASC,mBAAmBC,GAAW;IAC5C,KAAK,MAAMC,YAAYH,mBAAoB;QACzC,MAAMI,iBAAiBN,KAAKI,KAAKC;QACjC,MAAME,SAASN,WAAWK;QAC1B,IAAI,CAACC,QAAQ;YACX;QACF;QACA,OAAOD;IACT;AACF", "ignoreList": [0]}