(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,a,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},82557,e=>{"use strict";e.s(["default",()=>r]);var a=e.i(43476),t=e.i(57688),s=e.i(71645),n=e.i(45678),i=e.i(13642),l=e.i(75144);function r(){let{isDarkMode:e}=(0,l.useTheme)(),[r,o]=(0,s.useState)({nama:"",email:"",subject:"",pesan:""}),c=e=>{o({...r,[e.target.name]:e.target.value})};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col relative overflow-x-hidden",children:[(0,a.jsxs)("div",{className:"fixed inset-0 w-full h-full -z-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 transition-colors duration-300 ".concat(e?"bg-transparent":"bg-white")}),(0,a.jsx)("div",{className:"absolute inset-0 transition-all duration-300 ".concat(e?"brightness-[0.4]":""),children:(0,a.jsx)(t.default,{src:"/Home_Page/Backround Design.svg",alt:"Background with yellow circles",fill:!0,className:"object-cover",priority:!0,quality:100})})]}),(0,a.jsx)(n.default,{}),(0,a.jsx)("div",{className:"flex-grow pt-24 pb-8 px-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"rounded-3xl p-8 text-white shadow-2xl transition-colors duration-300 ".concat(e?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"),children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6 font-[family-name:var(--font-comfortaa)]",children:"Faculty Contact Info"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Head Office"}),(0,a.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung Dekanat FTUI Lt. 2",(0,a.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,a.jsx)("br",{}),"Kampus UI Depok",(0,a.jsx)("br",{}),"+6221 7863504, +6221 7863505"]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Kantor Humas dan Protokol"}),(0,a.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung GK IPAJI lantai 1",(0,a.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,a.jsx)("br",{}),"Kampus UI Depok",(0,a.jsx)("br",{}),"+6221 78888430 ext 106",(0,a.jsx)("br",{}),"<EMAIL>"]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("p",{className:"text-sm font-bold font-[family-name:var(--font-quicksand)]",children:"Mon – Fri 8:00A.M. – 4:00P.M."}),(0,a.jsx)("p",{className:"text-sm font-semibold font-[family-name:var(--font-quicksand)]",children:"Social Info"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("a",{href:"https://instagram.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/instagram 1.png",alt:"Instagram",fill:!0,className:"object-contain"})})}),(0,a.jsx)("a",{href:"https://linkedin.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/linkedin 1.png",alt:"LinkedIn",fill:!0,className:"object-contain"})})}),(0,a.jsx)("a",{href:"https://youtube.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/youtube 1.png",alt:"YouTube",fill:!0,className:"object-contain"})})}),(0,a.jsx)("a",{href:"https://facebook.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/facebook 1.png",alt:"Facebook",fill:!0,className:"object-contain"})})})]})]}),(0,a.jsx)("div",{className:"rounded-3xl p-8 shadow-2xl transition-colors duration-300 ".concat(e?"bg-gradient-to-br from-[#2d4a6e] to-[#1e3a5f]":"bg-gradient-to-br from-[#7a8a9a] to-[#5a7a9d]"),children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",r),alert("Pesan Anda telah terkirim!"),o({nama:"",email:"",subject:"",pesan:""})},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Nama"}),(0,a.jsx)("input",{type:"text",name:"nama",value:r.nama,onChange:c,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan nama Anda"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Email"}),(0,a.jsx)("input",{type:"email",name:"email",value:r.email,onChange:c,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan email Anda"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Subject"}),(0,a.jsx)("input",{type:"text",name:"subject",value:r.subject,onChange:c,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan subjek"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Pesan"}),(0,a.jsx)("textarea",{name:"pesan",value:r.pesan,onChange:c,required:!0,rows:6,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] resize-none font-[family-name:var(--font-quicksand)]",placeholder:"Tulis pesan Anda"})]}),(0,a.jsx)("button",{type:"submit",className:"w-full bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 font-bold py-3 rounded-xl transition-all shadow-lg font-[family-name:var(--font-comfortaa)]",children:"Kirim Pesan"})]})})]}),(0,a.jsxs)("div",{className:"rounded-3xl p-8 shadow-2xl transition-colors duration-300 ".concat(e?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"),children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6 text-white font-[family-name:var(--font-comfortaa)]",children:"Departement Contact Info"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>",telp:"WA 7270042 - 78849042",whatsapp:"WA 081212776702 (sekretariat Teknik Mesin)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Elektro",email:"<EMAIL>",telp:"+6221 7270078 - 7863504",whatsapp:"081289606440"},{name:"Departemen Teknik Metalurgi",email:"<EMAIL>",telp:"+6221 78849044",whatsapp:"081519996009"},{name:"Departemen Teknik Kimia",email:"<EMAIL>",telp:"+6221 7863516",whatsapp:"082112025025"},{name:"Departemen Arsitektur",email:"<EMAIL>",telp:"+6221 7270062",whatsapp:"081296661126"},{name:"Departemen Teknik Industri",email:"<EMAIL>",telp:"+6221 7270041",whatsapp:"082112345678"},{name:"Departemen Teknik Komputer",email:"<EMAIL>",telp:"+6221 7863512",whatsapp:"081234567890"}].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl transition-shadow",children:[(0,a.jsx)("h3",{className:"text-[#2d5a9e] font-bold text-sm mb-3 font-[family-name:var(--font-comfortaa)]",children:e.name}),(0,a.jsxs)("div",{className:"space-y-2 text-xs font-[family-name:var(--font-quicksand)]",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-600",children:e.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:"Telp"}),(0,a.jsx)("p",{className:"text-gray-600",children:e.telp})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:"Whatsapp Only"}),(0,a.jsx)("p",{className:"text-gray-600",children:e.whatsapp})]})]})]},t))})]})]})}),(0,a.jsx)(i.default,{})]})}}]);