from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import os
import shutil
from pathlib import Path
import logging
import traceback

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
    Settings,
    Document,
)
from llama_index.core.node_parser import SentenceSplitter
from llama_index.llms.ollama import Ollama
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

app = FastAPI(title="LlamaIndex RAG API")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Paths
DATA_DIR = Path("./data")
STORAGE_DIR = Path("./storage")
DATA_DIR.mkdir(exist_ok=True)
STORAGE_DIR.mkdir(exist_ok=True)

# Configure LlamaIndex Settings
try:
    logger.info("Initializing LlamaIndex settings...")
    Settings.llm = Ollama(
        model="llama3.2:latest",
        base_url=os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
        request_timeout=120.0
    )
    Settings.embed_model = HuggingFaceEmbedding(
        model_name="BAAI/bge-small-en-v1.5"
    )
    logger.info("LlamaIndex settings initialized successfully")
except Exception as e:
    logger.error(f"Error initializing LlamaIndex settings: {e}")
    logger.error(traceback.format_exc())

# Global index
index = None
query_engine = None


def initialize_index():
    """Initialize or load the index"""
    global index, query_engine
    
    try:
        # Try to load existing index
        if STORAGE_DIR.exists() and any(STORAGE_DIR.iterdir()):
            logger.info("Loading existing index...")
            storage_context = StorageContext.from_defaults(persist_dir=str(STORAGE_DIR))
            index = load_index_from_storage(storage_context)
        else:
            # Create new index if data exists
            if DATA_DIR.exists() and any(DATA_DIR.iterdir()):
                logger.info("Creating new index from documents...")
                
                # Load documents - SimpleDirectoryReader will auto-detect file types
                documents = SimpleDirectoryReader(
                    str(DATA_DIR),
                    recursive=True
                ).load_data()
                
                logger.info(f"Loaded {len(documents)} documents")
                
                if len(documents) == 0:
                    logger.warning("No documents could be loaded!")
                    return
                
                # Parse documents into nodes with better chunking
                parser = SentenceSplitter(
                    chunk_size=1024,
                    chunk_overlap=200
                )
                
                index = VectorStoreIndex.from_documents(
                    documents,
                    transformations=[parser]
                )
                index.storage_context.persist(persist_dir=str(STORAGE_DIR))
                logger.info("Index created and persisted")
            else:
                logger.info("No documents found. Index will be created when documents are uploaded.")
                return
        
        # Create query engine with RAG configuration
        query_engine = index.as_query_engine(
            similarity_top_k=3,
            response_mode="tree_summarize"
        )
        logger.info("Index initialized successfully!")
    except Exception as e:
        logger.error(f"Error initializing index: {e}")
        logger.error(traceback.format_exc())


# Pydantic models
class QueryRequest(BaseModel):
    question: str
    top_k: Optional[int] = 3


class QueryResponse(BaseModel):
    answer: str
    sources: Optional[List[str]] = None


class StatusResponse(BaseModel):
    status: str
    message: str
    documents_count: int


@app.on_event("startup")
async def startup_event():
    """Initialize index on startup"""
    initialize_index()


@app.get("/", response_model=StatusResponse)
async def root():
    """Root endpoint"""
    doc_count = len(list(DATA_DIR.glob("*.*"))) if DATA_DIR.exists() else 0
    return StatusResponse(
        status="ok",
        message="LlamaIndex RAG API is running",
        documents_count=doc_count
    )


@app.get("/health", response_model=StatusResponse)
async def health_check():
    """Health check endpoint"""
    doc_count = len(list(DATA_DIR.glob("*.*"))) if DATA_DIR.exists() else 0
    return StatusResponse(
        status="healthy",
        message="API is operational",
        documents_count=doc_count
    )


@app.post("/api/upload")
async def upload_document(file: UploadFile = File(...)):
    """Upload a document to the RAG system"""
    try:
        # Save uploaded file
        file_path = DATA_DIR / file.filename
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Reinitialize index with new document
        initialize_index()
        
        return {
            "status": "success",
            "message": f"File '{file.filename}' uploaded successfully",
            "filename": file.filename
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading file: {str(e)}")


@app.post("/api/query", response_model=QueryResponse)
async def query_documents(request: QueryRequest):
    """Query the RAG system"""
    global query_engine
    
    logger.info(f"Received query: {request.question}")
    
    if query_engine is None:
        logger.error("Query engine is None")
        raise HTTPException(
            status_code=400,
            detail="No index available. Please upload documents first."
        )
    
    try:
        # Query the index
        logger.info("Querying index...")
        response = query_engine.query(request.question)
        logger.info("Query successful")
        
        # Extract sources
        sources = []
        if hasattr(response, 'source_nodes'):
            logger.info(f"Found {len(response.source_nodes)} source nodes")
            sources = [node.node.get_text()[:200] + "..." for node in response.source_nodes]
        
        return QueryResponse(
            answer=str(response),
            sources=sources if sources else None
        )
    except Exception as e:
        logger.error(f"Error during query: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error querying: {str(e)}")


@app.get("/api/documents")
async def list_documents():
    """List all uploaded documents"""
    try:
        documents = [f.name for f in DATA_DIR.iterdir() if f.is_file()]
        return {
            "status": "success",
            "documents": documents,
            "count": len(documents)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing documents: {str(e)}")


@app.delete("/api/documents/{filename}")
async def delete_document(filename: str):
    """Delete a document and rebuild index"""
    try:
        file_path = DATA_DIR / filename
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="Document not found")
        
        file_path.unlink()
        
        # Reinitialize index
        initialize_index()
        
        return {
            "status": "success",
            "message": f"Document '{filename}' deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")


@app.post("/api/rebuild-index")
async def rebuild_index():
    """Rebuild the entire index"""
    try:
        # Clear storage
        if STORAGE_DIR.exists():
            shutil.rmtree(STORAGE_DIR)
            STORAGE_DIR.mkdir(exist_ok=True)
        
        # Reinitialize
        initialize_index()
        
        return {
            "status": "success",
            "message": "Index rebuilt successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error rebuilding index: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
