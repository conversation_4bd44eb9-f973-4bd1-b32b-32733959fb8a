{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, ReactNode } from 'react';\r\n\r\ninterface ThemeContextType {\r\n  isDarkMode: boolean;\r\n  toggleDarkMode: () => void;\r\n}\r\n\r\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\r\n\r\nexport function ThemeProvider({ children }: { children: ReactNode }) {\r\n  const [isDarkMode, setIsDarkMode] = useState(false);\r\n\r\n  const toggleDarkMode = () => {\r\n    setIsDarkMode(prev => !prev);\r\n  };\r\n\r\n  return (\r\n    <ThemeContext.Provider value={{ isDarkMode, toggleDarkMode }}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useTheme() {\r\n  const context = useContext(ThemeContext);\r\n  if (context === undefined) {\r\n    throw new Error('useTheme must be used within a ThemeProvider');\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AASA,MAAM,6BAAe,IAAA,sNAAa,EAA+B;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,MAAM,iBAAiB;QACrB,cAAc,CAAA,OAAQ,CAAC;IACzB;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAY;QAAe;kBACxD;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/components/Providers.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ThemeProvider } from '@/contexts/ThemeContext';\r\nimport { ReactNode } from 'react';\r\n\r\nexport default function Providers({ children }: { children: ReactNode }) {\r\n  return (\r\n    <ThemeProvider>\r\n      {children}\r\n    </ThemeProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKe,SAAS,UAAU,EAAE,QAAQ,EAA2B;IACrE,qBACE,8OAAC,iJAAa;kBACX;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}