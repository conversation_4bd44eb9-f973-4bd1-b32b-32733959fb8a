{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/src/contexts/ThemeContext.tsx", "turbopack:///[project]/src/components/Providers.tsx"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "'use client';\r\n\r\nimport { createContext, useContext, useState, ReactNode } from 'react';\r\n\r\ninterface ThemeContextType {\r\n  isDarkMode: boolean;\r\n  toggleDarkMode: () => void;\r\n}\r\n\r\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\r\n\r\nexport function ThemeProvider({ children }: { children: ReactNode }) {\r\n  const [isDarkMode, setIsDarkMode] = useState(false);\r\n\r\n  const toggleDarkMode = () => {\r\n    setIsDarkMode(prev => !prev);\r\n  };\r\n\r\n  return (\r\n    <ThemeContext.Provider value={{ isDarkMode, toggleDarkMode }}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useTheme() {\r\n  const context = useContext(ThemeContext);\r\n  if (context === undefined) {\r\n    throw new Error('useTheme must be used within a ThemeProvider');\r\n  }\r\n  return context;\r\n}\r\n", "'use client';\r\n\r\nimport { ThemeProvider } from '@/contexts/ThemeContext';\r\nimport { ReactNode } from 'react';\r\n\r\nexport default function Providers({ children }: { children: ReactNode }) {\r\n  return (\r\n    <ThemeProvider>\r\n      {children}\r\n    </ThemeProvider>\r\n  );\r\n}\r\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React"], "mappings": "0NA0BQG,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,+BCFxCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,uFCA9B,EAAA,EAAA,CAAA,CAAA,OAOA,IAAM,EAAe,CAAA,EAAA,EAAA,aAAA,AAAa,OAA+B,GAE1D,SAAS,EAAc,UAAE,CAAQ,CAA2B,EACjE,GAAM,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAM7C,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAa,QAAQ,CAAA,CAAC,MAAO,YAAE,EAAY,eALvB,KACrB,EAAc,GAAQ,CAAC,EACzB,CAG6D,WACxD,GAGP,CAEO,SAAS,IACd,IAAM,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,GAC3B,QAAgB,IAAZ,EACF,KADyB,CACnB,AAAI,MAAM,gDAElB,OAAO,CACT,kEC7BA,EAAA,EAAA,CAAA,CAAA,MAGe,SAAS,EAAU,UAAE,CAAQ,CAA2B,EACrE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACX,GAGP", "ignoreList": [0, 1, 2]}