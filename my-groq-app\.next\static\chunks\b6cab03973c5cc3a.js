(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},98183,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function s(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return s}})},95057,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return s},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let n=e.r(90809)._(e.r(98183)),o=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:r}=e,s=e.protocol||"",a=e.pathname||"",l=e.hash||"",i=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||o.test(s))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+s+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return s(e)}},18581,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(71645);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=s(e,n)),t&&(o.current=s(t,n))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18967,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return y},NormalizeError:function(){return x},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return m},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return u},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return j}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),s=0;s<n;s++)o[s]=arguments[s];return r||(r=!0,t=e(...o)),t}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>s.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,m=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class x extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function j(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return s}});let n=e.r(18967),o=e.r(52817);function s(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},84508,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},22016,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return x},useLinkStatus:function(){return y}});let n=e.r(90809),o=e.r(43476),s=n._(e.r(71645)),a=e.r(95057),l=e.r(8372),i=e.r(18581),u=e.r(18967),c=e.r(5550);e.r(33525);let d=e.r(91949),f=e.r(73668),p=e.r(99781);e.r(84508);let m=e.r(65165);function h(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function x(e){var t;let r,n,a,[x,y]=(0,s.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,s.useRef)(null),{href:j,as:v,children:N,prefetch:w=null,passHref:P,replace:S,shallow:O,scroll:_,onClick:E,onMouseEnter:T,onTouchStart:C,legacyBehavior:A=!1,onNavigate:k,ref:R,unstable_dynamicOnHover:U,...M}=e;r=N,A&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let I=s.default.useContext(l.AppRouterContext),L=!1!==w,D=!1!==w?null===(t=w)||"auto"===t?m.FetchStrategy.PPR:m.FetchStrategy.Full:m.FetchStrategy.PPR,{href:F,as:K}=s.default.useMemo(()=>{let e=h(j);return{href:e,as:v?h(v):e}},[j,v]);A&&(n=s.default.Children.only(r));let B=A?n&&"object"==typeof n&&n.ref:R,q=s.default.useCallback(e=>(null!==I&&(b.current=(0,d.mountLinkInstance)(e,F,I,D,L,y)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[L,F,I,D,y]),G={ref:(0,i.useMergedRef)(q,B),onClick(e){A||"function"!=typeof E||E(e),A&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,n,o,a,l){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}s.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==a||a,n.current)})}}(e,F,K,b,S,_,k))},onMouseEnter(e){A||"function"!=typeof T||T(e),A&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),I&&L&&(0,d.onNavigationIntent)(e.currentTarget,!0===U)},onTouchStart:function(e){A||"function"!=typeof C||C(e),A&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),I&&L&&(0,d.onNavigationIntent)(e.currentTarget,!0===U)}};return(0,u.isAbsoluteUrl)(K)?G.href=K:A&&!P&&("a"!==n.type||"href"in n.props)||(G.href=(0,c.addBasePath)(K)),a=A?s.default.cloneElement(n,G):(0,o.jsx)("a",{...M,...G,children:r}),(0,o.jsx)(g.Provider,{value:x,children:a})}let g=(0,s.createContext)(d.IDLE_LINK_STATUS),y=()=>(0,s.useContext)(g);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},82107,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(43476),r=e.i(71645),n=e.i(22016);function o(){let[e,o]=(0,r.useState)([]),[s,a]=(0,r.useState)(""),[l,i]=(0,r.useState)(!1),[u,c]=(0,r.useState)([]),[d,f]=(0,r.useState)(!1),[p,m]=(0,r.useState)(!1),h=(0,r.useRef)(null),x=(0,r.useRef)(null);(0,r.useEffect)(()=>{var e;null==(e=h.current)||e.scrollIntoView({behavior:"smooth"})},[e]),(0,r.useEffect)(()=>{g()},[]);let g=async()=>{try{let e=await fetch("/api/documents");if(e.ok){let t=await e.json();c(t.documents.map(e=>({name:e})))}}catch(e){console.error("Error fetching documents:",e)}},y=async e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(!r)return;f(!0);let n=new FormData;n.append("file",r);try{let e=await fetch("/api/documents",{method:"POST",body:n});if(e.ok)await g(),alert("Document uploaded successfully!"),m(!1);else{let t=await e.json();alert("Upload failed: ".concat(t.error))}}catch(e){console.error("Error uploading file:",e),alert("Failed to upload document")}finally{f(!1),x.current&&(x.current.value="")}},b=async e=>{if(e.preventDefault(),!s.trim()||l)return;let t={id:Date.now().toString(),role:"user",content:s};o(e=>[...e,t]),a(""),i(!0);try{let e=await fetch("/api/rag",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:s,top_k:3})});if(!e.ok)throw Error("Failed to fetch response");let t=await e.json(),r={id:(Date.now()+1).toString(),role:"assistant",content:t.answer,sources:t.sources};o(e=>[...e,r])}catch(t){console.error("Error:",t);let e={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running."};o(t=>[...t,e])}finally{i(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100",children:(0,t.jsxs)("div",{className:"mx-auto w-full max-w-6xl py-8 px-4",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"PIP FTUI - RAG Mode"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Chat with your documents using LlamaIndex & Groq"}),(0,t.jsxs)("div",{className:"flex justify-center gap-4 mt-4",children:[(0,t.jsx)(n.default,{href:"/",children:(0,t.jsx)("div",{className:"bg-white text-gray-700 border-2 border-gray-300 px-6 py-2 rounded-lg font-medium hover:border-blue-500 hover:text-blue-600 transition-colors cursor-pointer",children:"💬 Chat Mode"})}),(0,t.jsx)("div",{className:"bg-purple-500 text-white px-6 py-2 rounded-lg font-medium",children:"📚 RAG Mode"})]})]}),(0,t.jsxs)("div",{className:"flex gap-6",children:[(0,t.jsxs)("div",{className:"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Documents"}),(0,t.jsx)("button",{onClick:()=>m(!p),className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:p?"Cancel":"+ Upload"})]}),p&&(0,t.jsxs)("div",{className:"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300",children:[(0,t.jsx)("input",{ref:x,type:"file",onChange:y,disabled:d,accept:".pdf,.txt,.docx,.md,.html",className:"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer"}),d&&(0,t.jsx)("p",{className:"text-sm text-purple-600 mt-2",children:"Uploading..."})]}),(0,t.jsx)("div",{className:"overflow-y-auto h-[calc(100%-100px)]",children:0===u.length?(0,t.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"📄"}),(0,t.jsx)("p",{className:"text-sm",children:"No documents yet"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Upload a document to start"})]}):(0,t.jsx)("div",{className:"space-y-2",children:u.map((e,r)=>(0,t.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"📄"}),(0,t.jsx)("span",{className:"text-sm text-gray-700 truncate flex-1",children:e.name})]})},r))})})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[0===e.length?(0,t.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"🤖"}),(0,t.jsx)("p",{className:"text-lg",children:"Ask questions about your documents!"}),(0,t.jsx)("p",{className:"text-sm",children:"Upload documents and start asking questions."})]}):e.map(e=>(0,t.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,t.jsxs)("div",{className:"\n                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md\n                          ".concat("user"===e.role?"bg-gradient-to-r from-purple-500 to-pink-600 text-white":"bg-gray-100 text-gray-800 border border-gray-200","\n                        "),children:[(0,t.jsx)("div",{className:"text-xs mb-2 font-medium ".concat("user"===e.role?"text-purple-100":"text-gray-500"),children:"user"===e.role?"You":"RAG Assistant"}),(0,t.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-wrap ".concat("user"===e.role?"text-white":"text-gray-700"),children:e.content}),e.sources&&e.sources.length>0&&(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-300",children:[(0,t.jsx)("p",{className:"text-xs font-semibold text-gray-600 mb-2",children:"Sources:"}),(0,t.jsx)("div",{className:"space-y-2",children:e.sources.map((e,r)=>(0,t.jsx)("div",{className:"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200",children:e},r))})]})]})},e.id)),l&&(0,t.jsx)("div",{className:"flex justify-start",children:(0,t.jsx)("div",{className:"bg-gray-100 rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Searching documents..."})]})})}),(0,t.jsx)("div",{ref:h})]})}),(0,t.jsxs)("form",{onSubmit:b,className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1 relative",children:(0,t.jsx)("input",{value:s,onChange:e=>a(e.target.value),placeholder:"Ask a question about your documents...",disabled:l||0===u.length,className:"w-full rounded-2xl border-2 border-gray-200 px-6 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-200 disabled:bg-gray-50 disabled:cursor-not-allowed text-lg shadow-lg"})}),(0,t.jsx)("button",{type:"submit",disabled:l||!s.trim()||0===u.length,className:"rounded-2xl bg-gradient-to-r from-purple-500 to-pink-600 px-8 py-4 text-white font-medium hover:from-purple-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-200",children:l?"Searching...":"Ask"})]})]})]})]})})}}]);