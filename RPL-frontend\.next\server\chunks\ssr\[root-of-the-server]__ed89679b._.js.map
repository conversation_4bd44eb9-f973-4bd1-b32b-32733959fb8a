{"version": 3, "sources": ["turbopack:///[project]/src/components/HomePage.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/src/app/home/<USER>"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/HomePage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/HomePage.tsx\",\n    \"default\",\n);\n", "import HomePage from \"@/components/HomePage\";\r\n\r\nexport default function Home() {\r\n  return (\r\n    <HomePage />\r\n  );\r\n}\r\n"], "names": [], "mappings": "sPAEe,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,6RAA+R,EAC5T,4DACA,gEAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,yQAA2Q,EACxS,wCACA,sJCLJ,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAQ,CAAA,CAAA,EAEb", "ignoreList": [0]}