{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\nexport default function Navbar() {\r\n  const { isDarkMode, toggleDarkMode } = useTheme();\r\n\r\n  return (\r\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-colors duration-300 ${\r\n      isDarkMode ? 'bg-[#1e3a5f]' : 'bg-[#2d5a9e]'\r\n    }`}>\r\n      <div className=\"px-8 py-3 shadow-lg\">\r\n        <div className=\"flex items-center justify-between w-full\">\r\n          {/* Logo */}\r\n          <Link href=\"/\" className=\"flex items-center hover:opacity-90 transition-opacity\">\r\n            <Image \r\n              src=\"/Landing_Page/PIP LOGO 2.svg\"\r\n              alt=\"PIP FTUI Logo\"\r\n              width={160}\r\n              height={55}\r\n              className=\"h-12 w-auto\"\r\n            />\r\n          </Link>\r\n\r\n          {/* Navigation Links */}\r\n          <div className=\"flex items-center gap-8\">\r\n            {/* Theme toggle with sliding animation */}\r\n            <button\r\n              onClick={toggleDarkMode}\r\n              className=\"relative w-14 h-7 bg-white/20 rounded-full p-0.5 cursor-pointer transition-colors hover:bg-white/30\"\r\n              aria-label=\"Toggle theme\"\r\n            >\r\n              <div \r\n                className={`absolute top-0.5 left-0.5 w-6 h-6 rounded-full bg-white shadow-md transform transition-transform duration-300 ease-in-out flex items-center justify-center ${\r\n                  isDarkMode ? 'translate-x-7' : 'translate-x-0'\r\n                }`}\r\n              >\r\n                {isDarkMode ? (\r\n                  <span className=\"text-sm\">☀️</span>\r\n                ) : (\r\n                  <span className=\"text-sm\">🌙</span>\r\n                )}\r\n              </div>\r\n            </button>\r\n\r\n            <Link \r\n              href=\"/home\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Home\r\n            </Link>\r\n            <Link \r\n              href=\"/documents\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Documents\r\n            </Link>\r\n            <Link \r\n              href=\"/academics\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Academics\r\n            </Link>\r\n            <Link \r\n              href=\"/contacts\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Contacts\r\n            </Link>\r\n            <Link \r\n              href=\"/prototypetesting\"\r\n              className=\"text-yellow-300 font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-400 transition-colors border-2 border-yellow-300 px-4 py-1 rounded-lg\"\r\n            >\r\n              🧪 Prototype\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,IAAA,+IAAQ;IAE/C,qBACE,6LAAC;QAAI,WAAW,AAAC,kEAEhB,OADC,aAAa,iBAAiB;kBAE9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,0KAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCACC,WAAW,AAAC,8JAEX,OADC,aAAa,kBAAkB;8CAGhC,2BACC,6LAAC;wCAAK,WAAU;kDAAU;;;;;6DAE1B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;0CAKhC,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA5EwB;;QACiB,+IAAQ;;;KADzB", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\nexport default function Footer() {\r\n  const { isDarkMode } = useTheme();\r\n  \r\n  return (\r\n    <footer className={`text-white mt-auto transition-colors duration-300 ${\r\n      isDarkMode ? 'bg-[#1e3a5f]' : 'bg-[#2d5a9e]'\r\n    }`}>\r\n      <div className=\"px-8 py-8\">\r\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center gap-8 w-full\">\r\n          {/* Logo and Copyright */}\r\n          <div className=\"flex flex-col gap-3\">\r\n            <Image \r\n              src=\"/Landing_Page/PIP LOGO 2.svg\"\r\n              alt=\"PIP FTUI Logo\"\r\n              width={220}\r\n              height={80}\r\n              className=\"h-20 w-auto\"\r\n            />\r\n            <p className=\"text-white font-[family-name:var(--font-comfortaa)] text-base font-semibold\">\r\n              @ PIP All Rights Reserved.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Address and Social Links */}\r\n          <div className=\"flex flex-col items-start md:items-end gap-4\">\r\n            {/* Address */}\r\n            <div className=\"flex items-start gap-2 text-white\">\r\n              <Image \r\n                src=\"/Footer/marker-pin-02.png\"\r\n                alt=\"Location Pin\"\r\n                width={20}\r\n                height={20}\r\n                className=\"mt-0.5 flex-shrink-0\"\r\n              />\r\n              <p className=\"text-sm font-[family-name:var(--font-comfortaa)] max-w-md text-left md:text-right leading-relaxed\">\r\n                Pusgiwa UI, Gedung D Lt. 7, Jl. Prof. Dr. Fuad Hassan, Kukusan, Kecamatan Beji, Kota Depok, Jawa Barat 16425\r\n              </p>\r\n            </div>\r\n\r\n            {/* Social Media Icons */}\r\n            <div className=\"flex items-center gap-3\">\r\n              <Link \r\n                href=\"https://instagram.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"Instagram\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/instagram 1.png\"\r\n                  alt=\"Instagram\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n              <Link \r\n                href=\"https://linkedin.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"LinkedIn\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/linkedin 1.png\"\r\n                  alt=\"LinkedIn\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n              <Link \r\n                href=\"https://youtube.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"YouTube\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/youtube 1.png\"\r\n                  alt=\"YouTube\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n              <Link \r\n                href=\"https://facebook.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"Facebook\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/facebook 1.png\"\r\n                  alt=\"Facebook\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,+IAAQ;IAE/B,qBACE,6LAAC;QAAO,WAAW,AAAC,qDAEnB,OADC,aAAa,iBAAiB;kBAE9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2IAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,6LAAC;gCAAE,WAAU;0CAA8E;;;;;;;;;;;;kCAM7F,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2IAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAAoG;;;;;;;;;;;;0CAMnH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B;GAvGwB;;QACC,+IAAQ;;;KADT", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/components/ContactPage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Image from 'next/image';\r\nimport { useState } from 'react';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\nexport default function ContactPage() {\r\n  const { isDarkMode } = useTheme();\r\n  const [formData, setFormData] = useState({\r\n    nama: '',\r\n    email: '',\r\n    subject: '',\r\n    pesan: '',\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    // Handle form submission\r\n    console.log('Form submitted:', formData);\r\n    alert('Pesan Anda telah terkirim!');\r\n    setFormData({ nama: '', email: '', subject: '', pesan: '' });\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col relative overflow-x-hidden\">\r\n      {/* Background with Yellow Circles */}\r\n      <div className=\"fixed inset-0 w-full h-full -z-10\">\r\n        {/* White background layer for light mode */}\r\n        <div className={`absolute inset-0 transition-colors duration-300 ${\r\n          isDarkMode ? 'bg-transparent' : 'bg-white'\r\n        }`}></div>\r\n        {/* SVG overlay with brightness control */}\r\n        <div className={`absolute inset-0 transition-all duration-300 ${\r\n          isDarkMode ? 'brightness-[0.4]' : ''\r\n        }`}>\r\n          <Image\r\n            src=\"/Home_Page/Backround Design.svg\"\r\n            alt=\"Background with yellow circles\"\r\n            fill\r\n            className=\"object-cover\"\r\n            priority\r\n            quality={100}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <Navbar />\r\n\r\n      <div className=\"flex-grow pt-24 pb-8 px-8\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          {/* Faculty and Department Contact Section */}\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\r\n            {/* Faculty Contact Info */}\r\n            <div className={`rounded-3xl p-8 text-white shadow-2xl transition-colors duration-300 ${\r\n              isDarkMode\r\n                ? 'bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]'\r\n                : 'bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]'\r\n            }`}>\r\n              <h2 className=\"text-2xl font-bold mb-6 font-[family-name:var(--font-comfortaa)]\">\r\n                Faculty Contact Info\r\n              </h2>\r\n\r\n              {/* Head Office */}\r\n              <div className=\"mb-6\">\r\n                <h3 className=\"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n                  Head Office\r\n                </h3>\r\n                <p className=\"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]\">\r\n                  Gedung Dekanat FTUI Lt. 2<br />\r\n                  Fakultas Teknik Universitas Indonesia<br />\r\n                  Kampus UI Depok<br />\r\n                  +6221 7863504, +6221 7863505\r\n                </p>\r\n              </div>\r\n\r\n              {/* Kantor Humas dan Protokol */}\r\n              <div className=\"mb-6\">\r\n                <h3 className=\"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n                  Kantor Humas dan Protokol\r\n                </h3>\r\n                <p className=\"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]\">\r\n                  Gedung GK IPAJI lantai 1<br />\r\n                  Fakultas Teknik Universitas Indonesia<br />\r\n                  Kampus UI Depok<br />\r\n                  +6221 78888430 ext 106<br />\r\n                  <EMAIL>\r\n                </p>\r\n              </div>\r\n\r\n              {/* Operating Hours */}\r\n              <div className=\"mb-6\">\r\n                <p className=\"text-sm font-bold font-[family-name:var(--font-quicksand)]\">\r\n                  Mon – Fri 8:00A.M. – 4:00P.M.\r\n                </p>\r\n                <p className=\"text-sm font-semibold font-[family-name:var(--font-quicksand)]\">\r\n                  Social Info\r\n                </p>\r\n              </div>\r\n\r\n              {/* Social Media Icons */}\r\n              <div className=\"flex gap-4\">\r\n                <a href=\"https://instagram.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/instagram 1.png\" alt=\"Instagram\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n                <a href=\"https://linkedin.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/linkedin 1.png\" alt=\"LinkedIn\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n                <a href=\"https://youtube.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/youtube 1.png\" alt=\"YouTube\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n                <a href=\"https://facebook.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:opacity-80 transition-opacity\">\r\n                  <div className=\"w-8 h-8 relative\">\r\n                    <Image src=\"/Footer/facebook 1.png\" alt=\"Facebook\" fill className=\"object-contain\" />\r\n                  </div>\r\n                </a>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Contact Form */}\r\n            <div className={`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${\r\n              isDarkMode\r\n                ? 'bg-gradient-to-br from-[#2d4a6e] to-[#1e3a5f]'\r\n                : 'bg-gradient-to-br from-[#7a8a9a] to-[#5a7a9d]'\r\n            }`}>\r\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Nama\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"nama\"\r\n                    value={formData.nama}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Masukkan nama Anda\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Email\r\n                  </label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Masukkan email Anda\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Subject\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"subject\"\r\n                    value={formData.subject}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Masukkan subjek\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]\">\r\n                    Pesan\r\n                  </label>\r\n                  <textarea\r\n                    name=\"pesan\"\r\n                    value={formData.pesan}\r\n                    onChange={handleChange}\r\n                    required\r\n                    rows={6}\r\n                    className=\"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] resize-none font-[family-name:var(--font-quicksand)]\"\r\n                    placeholder=\"Tulis pesan Anda\"\r\n                  />\r\n                </div>\r\n\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 font-bold py-3 rounded-xl transition-all shadow-lg font-[family-name:var(--font-comfortaa)]\"\r\n                >\r\n                  Kirim Pesan\r\n                </button>\r\n              </form>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Department Contact Info */}\r\n          <div className={`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${\r\n            isDarkMode\r\n              ? 'bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]'\r\n              : 'bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]'\r\n          }`}>\r\n            <h2 className=\"text-2xl font-bold mb-6 text-white font-[family-name:var(--font-comfortaa)]\">\r\n              Departement Contact Info\r\n            </h2>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n              {/* Department Cards */}\r\n              {[\r\n                {\r\n                  name: 'Departemen Teknik Sipil',\r\n                  email: '<EMAIL>, <EMAIL>',\r\n                  telp: '+6221 7270029 - 7270028',\r\n                  whatsapp: '082211135202 (Sekretariat DTS)',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Sipil',\r\n                  email: '<EMAIL>',\r\n                  telp: 'WA 7270042 - 78849042',\r\n                  whatsapp: 'WA 081212776702 (sekretariat Teknik Mesin)',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Sipil',\r\n                  email: '<EMAIL>, <EMAIL>',\r\n                  telp: '+6221 7270029 - 7270028',\r\n                  whatsapp: '082211135202 (Sekretariat DTS)',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Elektro',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7270078 - 7863504',\r\n                  whatsapp: '081289606440',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Metalurgi',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 78849044',\r\n                  whatsapp: '081519996009',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Kimia',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7863516',\r\n                  whatsapp: '082112025025',\r\n                },\r\n                {\r\n                  name: 'Departemen Arsitektur',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7270062',\r\n                  whatsapp: '081296661126',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Industri',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7270041',\r\n                  whatsapp: '082112345678',\r\n                },\r\n                {\r\n                  name: 'Departemen Teknik Komputer',\r\n                  email: '<EMAIL>',\r\n                  telp: '+6221 7863512',\r\n                  whatsapp: '081234567890',\r\n                },\r\n              ].map((dept, idx) => (\r\n                <div\r\n                  key={idx}\r\n                  className=\"bg-white/90 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl transition-shadow\"\r\n                >\r\n                  <h3 className=\"text-[#2d5a9e] font-bold text-sm mb-3 font-[family-name:var(--font-comfortaa)]\">\r\n                    {dept.name}\r\n                  </h3>\r\n                  <div className=\"space-y-2 text-xs font-[family-name:var(--font-quicksand)]\">\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-700\">Email</p>\r\n                      <p className=\"text-gray-600\">{dept.email}</p>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-700\">Telp</p>\r\n                      <p className=\"text-gray-600\">{dept.telp}</p>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-semibold text-gray-700\">Whatsapp Only</p>\r\n                      <p className=\"text-gray-600\">{dept.whatsapp}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,+IAAQ;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,MAAM;QACN,YAAY;YAAE,MAAM;YAAI,OAAO;YAAI,SAAS;YAAI,OAAO;QAAG;IAC5D;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,AAAC,mDAEhB,OADC,aAAa,mBAAmB;;;;;;kCAGlC,6LAAC;wBAAI,WAAW,AAAC,gDAEhB,OADC,aAAa,qBAAqB;kCAElC,cAAA,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,IAAI;4BACJ,WAAU;4BACV,QAAQ;4BACR,SAAS;;;;;;;;;;;;;;;;;0BAKf,6LAAC,0IAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAW,AAAC,wEAIhB,OAHC,aACI,kDACA;;sDAEJ,6LAAC;4CAAG,WAAU;sDAAmE;;;;;;sDAKjF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAkE;;;;;;8DAGhF,6LAAC;oDAAE,WAAU;;wDAAmE;sEACrD,6LAAC;;;;;wDAAK;sEACM,6LAAC;;;;;wDAAK;sEAC5B,6LAAC;;;;;wDAAK;;;;;;;;;;;;;sDAMzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAkE;;;;;;8DAGhF,6LAAC;oDAAE,WAAU;;wDAAmE;sEACtD,6LAAC;;;;;wDAAK;sEACO,6LAAC;;;;;wDAAK;sEAC5B,6LAAC;;;;;wDAAK;sEACC,6LAAC;;;;;wDAAK;;;;;;;;;;;;;sDAMhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA6D;;;;;;8DAG1E,6LAAC;oDAAE,WAAU;8DAAiE;;;;;;;;;;;;sDAMhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,MAAK;oDAAwB,QAAO;oDAAS,KAAI;oDAAsB,WAAU;8DAClF,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2IAAK;4DAAC,KAAI;4DAA0B,KAAI;4DAAY,IAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGxE,6LAAC;oDAAE,MAAK;oDAAuB,QAAO;oDAAS,KAAI;oDAAsB,WAAU;8DACjF,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2IAAK;4DAAC,KAAI;4DAAyB,KAAI;4DAAW,IAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGtE,6LAAC;oDAAE,MAAK;oDAAsB,QAAO;oDAAS,KAAI;oDAAsB,WAAU;8DAChF,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2IAAK;4DAAC,KAAI;4DAAwB,KAAI;4DAAU,IAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGpE,6LAAC;oDAAE,MAAK;oDAAuB,QAAO;oDAAS,KAAI;oDAAsB,WAAU;8DACjF,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2IAAK;4DAAC,KAAI;4DAAyB,KAAI;4DAAW,IAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1E,6LAAC;oCAAI,WAAW,AAAC,6DAIhB,OAHC,aACI,kDACA;8CAEJ,cAAA,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAuF;;;;;;kEAGxG,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAuF;;;;;;kEAGxG,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAuF;;;;;;kEAGxG,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAuF;;;;;;kEAGxG,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BAAI,WAAW,AAAC,6DAIhB,OAHC,aACI,kDACA;;8CAEJ,6LAAC;oCAAG,WAAU;8CAA8E;;;;;;8CAI5F,6LAAC;oCAAI,WAAU;8CAEZ;wCACC;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,MAAM;4CACN,UAAU;wCACZ;qCACD,CAAC,GAAG,CAAC,CAAC,MAAM,oBACX,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA8B;;;;;;8EAC3C,6LAAC;oEAAE,WAAU;8EAAiB,KAAK,KAAK;;;;;;;;;;;;sEAE1C,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA8B;;;;;;8EAC3C,6LAAC;oEAAE,WAAU;8EAAiB,KAAK,IAAI;;;;;;;;;;;;sEAEzC,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA8B;;;;;;8EAC3C,6LAAC;oEAAE,WAAU;8EAAiB,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;2CAjB1C;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA2BjB,6LAAC,0IAAM;;;;;;;;;;;AAGb;GA7SwB;;QACC,+IAAQ;;;KADT", "debugId": null}}]}