module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},98144,(e,t,r)=>{},37588,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>C,"routeModule",()=>w,"serverHooks",()=>E,"workAsyncStorage",()=>y,"workUnitAsyncStorage",()=>m],37588);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),h=e.i(26937),R=e.i(10372),x=e.i(93695);e.i(52474);var v=e.i(220);async function g(e){try{let{question:t,top_k:r=3}=await e.json(),a=await fetch("http://localhost:8000/api/query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:t,top_k:r})});if(!a.ok){let e=await a.json();return new Response(JSON.stringify({error:e.detail||"Failed to query RAG"}),{status:a.status,headers:{"Content-Type":"application/json"}})}let n=await a.json();return new Response(JSON.stringify(n),{headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Error querying RAG:",e),new Response(JSON.stringify({error:"Internal server error"}),{status:500,headers:{"Content-Type":"application/json"}})}}e.s(["POST",()=>g],42014);var f=e.i(42014);let w=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/rag/route",pathname:"/api/rag",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/rag/route.ts",nextConfigOutput:"",userland:f}),{workAsyncStorage:y,workUnitAsyncStorage:m,serverHooks:E}=w;function C(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:m})}async function A(e,t,a){var g;let f="/api/rag/route";f=f.replace(/\/index$/,"")||"/";let y=await w.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!y)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:m,params:E,nextConfig:C,isDraftMode:A,prerenderManifest:T,routerServerContext:N,isOnDemandRevalidate:O,revalidateOnlyGenerated:b,resolvedPathname:P}=y,S=(0,s.normalizeAppPath)(f),j=!!(T.dynamicRoutes[S]||T.routes[P]);if(j&&!A){let e=!!T.routes[P],t=T.dynamicRoutes[S];if(t&&!1===t.fallback&&!e)throw new x.NoFallbackError}let q=null;!j||w.isDev||A||(q="/index"===(q=P)?"/":q);let k=!0===w.isDev||!j,_=j&&!k,H=e.method||"GET",U=(0,o.getTracer)(),I=U.getActiveScopeSpan(),M={params:E,prerenderManifest:T,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:k,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=C.experimental)?void 0:g.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>w.onRequestError(e,t,a,N)},sharedContext:{buildId:m}},D=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let s=async r=>w.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${H} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${H} ${e.url}`)}),i=async o=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&O&&b&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!j)return await (0,p.sendResponse)(D,F,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[R.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=R.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=R.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:v.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await w.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:O})},N),t}},x=await w.handleResponse({req:e,nextConfig:C,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:T,isRoutePPREnabled:!1,isOnDemandRevalidate:O,revalidateOnlyGenerated:b,responseGenerator:d,waitUntil:a.waitUntil});if(!j)return null;if((null==x||null==(i=x.value)?void 0:i.kind)!==v.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==x||null==(l=x.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",O?"REVALIDATED":x.isMiss?"MISS":x.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(x.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&j||g.delete(R.NEXT_CACHE_TAGS_HEADER),!x.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,h.getCacheControlHeader)(x.cacheControl)),await (0,p.sendResponse)(D,F,new Response(x.value.body,{headers:g,status:x.value.status||200})),null};I?await i(I):await U.withPropagatedContext(e.headers,()=>U.trace(d.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},i))}catch(t){if(t instanceof x.NoFallbackError||await w.onRequestError(e,t,{routerKind:"App Router",routePath:S,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:O})}),j)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__a41950d8._.js.map