module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},84245,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(87924),c=a.i(71987),d=a.i(72131),e=a.i(73885),f=a.i(56283),g=a.i(9453);function h(){let{isDarkMode:a}=(0,g.useTheme)(),[h,i]=(0,d.useState)({nama:"",email:"",subject:"",pesan:""}),j=a=>{i({...h,[a.target.name]:a.target.value})};return(0,b.jsxs)("div",{className:"min-h-screen flex flex-col relative overflow-x-hidden",children:[(0,b.jsxs)("div",{className:"fixed inset-0 w-full h-full -z-10",children:[(0,b.jsx)("div",{className:`absolute inset-0 transition-colors duration-300 ${a?"bg-transparent":"bg-white"}`}),(0,b.jsx)("div",{className:`absolute inset-0 transition-all duration-300 ${a?"brightness-[0.4]":""}`,children:(0,b.jsx)(c.default,{src:"/Home_Page/Backround Design.svg",alt:"Background with yellow circles",fill:!0,className:"object-cover",priority:!0,quality:100})})]}),(0,b.jsx)(e.default,{}),(0,b.jsx)("div",{className:"flex-grow pt-24 pb-8 px-8",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,b.jsxs)("div",{className:`rounded-3xl p-8 text-white shadow-2xl transition-colors duration-300 ${a?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"}`,children:[(0,b.jsx)("h2",{className:"text-2xl font-bold mb-6 font-[family-name:var(--font-comfortaa)]",children:"Faculty Contact Info"}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Head Office"}),(0,b.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung Dekanat FTUI Lt. 2",(0,b.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,b.jsx)("br",{}),"Kampus UI Depok",(0,b.jsx)("br",{}),"+6221 7863504, +6221 7863505"]})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Kantor Humas dan Protokol"}),(0,b.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung GK IPAJI lantai 1",(0,b.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,b.jsx)("br",{}),"Kampus UI Depok",(0,b.jsx)("br",{}),"+6221 78888430 ext 106",(0,b.jsx)("br",{}),"<EMAIL>"]})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("p",{className:"text-sm font-bold font-[family-name:var(--font-quicksand)]",children:"Mon – Fri 8:00A.M. – 4:00P.M."}),(0,b.jsx)("p",{className:"text-sm font-semibold font-[family-name:var(--font-quicksand)]",children:"Social Info"})]}),(0,b.jsxs)("div",{className:"flex gap-4",children:[(0,b.jsx)("a",{href:"https://instagram.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/instagram 1.png",alt:"Instagram",fill:!0,className:"object-contain"})})}),(0,b.jsx)("a",{href:"https://linkedin.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/linkedin 1.png",alt:"LinkedIn",fill:!0,className:"object-contain"})})}),(0,b.jsx)("a",{href:"https://youtube.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/youtube 1.png",alt:"YouTube",fill:!0,className:"object-contain"})})}),(0,b.jsx)("a",{href:"https://facebook.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/facebook 1.png",alt:"Facebook",fill:!0,className:"object-contain"})})})]})]}),(0,b.jsx)("div",{className:`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${a?"bg-gradient-to-br from-[#2d4a6e] to-[#1e3a5f]":"bg-gradient-to-br from-[#7a8a9a] to-[#5a7a9d]"}`,children:(0,b.jsxs)("form",{onSubmit:a=>{a.preventDefault(),console.log("Form submitted:",h),alert("Pesan Anda telah terkirim!"),i({nama:"",email:"",subject:"",pesan:""})},className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Nama"}),(0,b.jsx)("input",{type:"text",name:"nama",value:h.nama,onChange:j,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan nama Anda"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Email"}),(0,b.jsx)("input",{type:"email",name:"email",value:h.email,onChange:j,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan email Anda"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Subject"}),(0,b.jsx)("input",{type:"text",name:"subject",value:h.subject,onChange:j,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan subjek"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Pesan"}),(0,b.jsx)("textarea",{name:"pesan",value:h.pesan,onChange:j,required:!0,rows:6,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] resize-none font-[family-name:var(--font-quicksand)]",placeholder:"Tulis pesan Anda"})]}),(0,b.jsx)("button",{type:"submit",className:"w-full bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 font-bold py-3 rounded-xl transition-all shadow-lg font-[family-name:var(--font-comfortaa)]",children:"Kirim Pesan"})]})})]}),(0,b.jsxs)("div",{className:`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${a?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"}`,children:[(0,b.jsx)("h2",{className:"text-2xl font-bold mb-6 text-white font-[family-name:var(--font-comfortaa)]",children:"Departement Contact Info"}),(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>",telp:"WA 7270042 - 78849042",whatsapp:"WA 081212776702 (sekretariat Teknik Mesin)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Elektro",email:"<EMAIL>",telp:"+6221 7270078 - 7863504",whatsapp:"081289606440"},{name:"Departemen Teknik Metalurgi",email:"<EMAIL>",telp:"+6221 78849044",whatsapp:"081519996009"},{name:"Departemen Teknik Kimia",email:"<EMAIL>",telp:"+6221 7863516",whatsapp:"082112025025"},{name:"Departemen Arsitektur",email:"<EMAIL>",telp:"+6221 7270062",whatsapp:"081296661126"},{name:"Departemen Teknik Industri",email:"<EMAIL>",telp:"+6221 7270041",whatsapp:"082112345678"},{name:"Departemen Teknik Komputer",email:"<EMAIL>",telp:"+6221 7863512",whatsapp:"081234567890"}].map((a,c)=>(0,b.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl transition-shadow",children:[(0,b.jsx)("h3",{className:"text-[#2d5a9e] font-bold text-sm mb-3 font-[family-name:var(--font-comfortaa)]",children:a.name}),(0,b.jsxs)("div",{className:"space-y-2 text-xs font-[family-name:var(--font-quicksand)]",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-semibold text-gray-700",children:"Email"}),(0,b.jsx)("p",{className:"text-gray-600",children:a.email})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-semibold text-gray-700",children:"Telp"}),(0,b.jsx)("p",{className:"text-gray-600",children:a.telp})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-semibold text-gray-700",children:"Whatsapp Only"}),(0,b.jsx)("p",{className:"text-gray-600",children:a.whatsapp})]})]})]},c))})]})]})}),(0,b.jsx)(f.default,{})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__3260618b._.js.map