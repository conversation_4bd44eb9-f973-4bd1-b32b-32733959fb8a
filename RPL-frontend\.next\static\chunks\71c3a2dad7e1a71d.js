(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,a)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"warnOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},4322,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(43476),a=e.i(71645),s=e.i(22016),r=e.i(45678),l=e.i(13642);function n(){let[e,n]=(0,a.useState)([]),[d,i]=(0,a.useState)(""),[o,c]=(0,a.useState)(!1),[x,m]=(0,a.useState)([]),[u,f]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!1),g=(0,a.useRef)(null),b=(0,a.useRef)(null);(0,a.useEffect)(()=>{g.current&&(g.current.scrollTop=g.current.scrollHeight)},[e]),(0,a.useEffect)(()=>{y()},[]);let y=async()=>{try{let e=await fetch("/api/documents");if(e.ok){let t=await e.json();m(t.documents.map(e=>({name:e})))}}catch(e){console.error("Error fetching documents:",e)}},j=async e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(!a)return;f(!0);let s=new FormData;s.append("file",a);try{let e=await fetch("/api/documents",{method:"POST",body:s});if(e.ok)await y(),alert("Document uploaded successfully!"),p(!1);else{let t=await e.json();alert("Upload failed: ".concat(t.error))}}catch(e){console.error("Error uploading file:",e),alert("Failed to upload document")}finally{f(!1),b.current&&(b.current.value="")}},v=async e=>{if(e.preventDefault(),!d.trim()||o)return;let t={id:Date.now().toString(),role:"user",content:d};n(e=>[...e,t]),i(""),c(!0);try{let e=await fetch("/api/rag",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:d,top_k:3})});if(!e.ok)throw Error("Failed to fetch response");let t=await e.json(),a={id:(Date.now()+1).toString(),role:"assistant",content:t.answer,sources:t.sources};n(e=>[...e,a])}catch(t){console.error("Error:",t);let e={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running."};n(t=>[...t,e])}finally{c(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",style:{background:"linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"},children:[(0,t.jsx)(r.default,{}),(0,t.jsxs)("div",{className:"mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]",children:"PIP FTUI - RAG Mode"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]",children:"Retrieval-Augmented Generation"})]}),(0,t.jsx)("p",{className:"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]",children:"Cari informasi FTUI berdasarkan dokumen yang tersedia"}),(0,t.jsxs)("div",{className:"flex justify-center gap-4 mt-6",children:[(0,t.jsx)(s.default,{href:"/chat",children:(0,t.jsx)("div",{className:"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]",children:"💬 Chat Mode"})}),(0,t.jsx)("div",{className:"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]",children:"📚 RAG Mode"})]})]}),(0,t.jsxs)("div",{className:"flex gap-6",children:[(0,t.jsxs)("div",{className:"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Documents"}),(0,t.jsx)("button",{onClick:()=>p(!h),className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:h?"Cancel":"+ Upload"})]}),h&&(0,t.jsxs)("div",{className:"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300",children:[(0,t.jsx)("input",{ref:b,type:"file",onChange:j,disabled:u,accept:".pdf,.txt,.docx,.md,.html",className:"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer"}),u&&(0,t.jsx)("p",{className:"text-sm text-purple-600 mt-2",children:"Uploading..."})]}),(0,t.jsx)("div",{className:"overflow-y-auto h-[calc(100%-100px)]",children:0===x.length?(0,t.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"📄"}),(0,t.jsx)("p",{className:"text-sm",children:"No documents yet"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Upload a document to start"})]}):(0,t.jsx)("div",{className:"space-y-2",children:x.map((e,a)=>(0,t.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:"📄"}),(0,t.jsx)("span",{className:"text-sm text-gray-700 truncate flex-1",children:e.name})]})},a))})})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{ref:g,className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[0===e.length?(0,t.jsxs)("div",{className:"text-center text-[#346ad5] mt-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"📚"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:"Tanyakan tentang informasi FTUI"}),(0,t.jsx)("p",{className:"text-sm",children:"Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan."})]}):e.map(e=>(0,t.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,t.jsxs)("div",{className:"\n                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md\n                          ".concat("user"===e.role?"bg-[#346ad5] text-white":"bg-white text-gray-800 border border-gray-200","\n                        "),children:[(0,t.jsx)("div",{className:"text-xs mb-2 font-medium ".concat("user"===e.role?"text-blue-100":"text-[#346ad5]"),children:"user"===e.role?"You":"PIP RAG Assistant"}),(0,t.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-wrap ".concat("user"===e.role?"text-white":"text-gray-700"),children:e.content}),e.sources&&e.sources.length>0&&(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-300",children:[(0,t.jsx)("p",{className:"text-xs font-semibold text-gray-600 mb-2",children:"Sources:"}),(0,t.jsx)("div",{className:"space-y-2",children:e.sources.map((e,a)=>(0,t.jsx)("div",{className:"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200",children:e},a))})]})]})},e.id)),o&&(0,t.jsx)("div",{className:"flex justify-start",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-[#fae664] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,t.jsx)("span",{className:"text-sm text-[#346ad5]",children:"Mencari di dokumen..."})]})})})]})}),(0,t.jsx)("form",{onSubmit:v,className:"relative flex-shrink-0",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4",children:[(0,t.jsx)("input",{value:d,onChange:e=>i(e.target.value),placeholder:"ASK PIP...",disabled:o||0===x.length,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,t.jsx)("button",{type:"submit",disabled:o||!d.trim()||0===x.length,className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]})]})]}),(0,t.jsx)(l.default,{})]})}}]);