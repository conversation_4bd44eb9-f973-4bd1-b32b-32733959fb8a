{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\nexport default function Navbar() {\r\n  const { isDarkMode, toggleDarkMode } = useTheme();\r\n\r\n  return (\r\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-colors duration-300 ${\r\n      isDarkMode ? 'bg-[#1e3a5f]' : 'bg-[#2d5a9e]'\r\n    }`}>\r\n      <div className=\"px-8 py-3 shadow-lg\">\r\n        <div className=\"flex items-center justify-between w-full\">\r\n          {/* Logo */}\r\n          <Link href=\"/\" className=\"flex items-center hover:opacity-90 transition-opacity\">\r\n            <Image \r\n              src=\"/Landing_Page/PIP LOGO 2.svg\"\r\n              alt=\"PIP FTUI Logo\"\r\n              width={160}\r\n              height={55}\r\n              className=\"h-12 w-auto\"\r\n            />\r\n          </Link>\r\n\r\n          {/* Navigation Links */}\r\n          <div className=\"flex items-center gap-8\">\r\n            {/* Theme toggle with sliding animation */}\r\n            <button\r\n              onClick={toggleDarkMode}\r\n              className=\"relative w-14 h-7 bg-white/20 rounded-full p-0.5 cursor-pointer transition-colors hover:bg-white/30\"\r\n              aria-label=\"Toggle theme\"\r\n            >\r\n              <div \r\n                className={`absolute top-0.5 left-0.5 w-6 h-6 rounded-full bg-white shadow-md transform transition-transform duration-300 ease-in-out flex items-center justify-center ${\r\n                  isDarkMode ? 'translate-x-7' : 'translate-x-0'\r\n                }`}\r\n              >\r\n                {isDarkMode ? (\r\n                  <span className=\"text-sm\">☀️</span>\r\n                ) : (\r\n                  <span className=\"text-sm\">🌙</span>\r\n                )}\r\n              </div>\r\n            </button>\r\n\r\n            <Link \r\n              href=\"/home\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Home\r\n            </Link>\r\n            <Link \r\n              href=\"/documents\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Documents\r\n            </Link>\r\n            <Link \r\n              href=\"/academics\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Academics\r\n            </Link>\r\n            <Link \r\n              href=\"/contacts\"\r\n              className=\"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors\"\r\n            >\r\n              Contacts\r\n            </Link>\r\n            <Link \r\n              href=\"/prototypetesting\"\r\n              className=\"text-yellow-300 font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-400 transition-colors border-2 border-yellow-300 px-4 py-1 rounded-lg\"\r\n            >\r\n              🧪 Prototype\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,IAAA,+IAAQ;IAE/C,qBACE,6LAAC;QAAI,WAAW,AAAC,kEAEhB,OADC,aAAa,iBAAiB;kBAE9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,0KAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCACC,WAAW,AAAC,8JAEX,OADC,aAAa,kBAAkB;8CAGhC,2BACC,6LAAC;wCAAK,WAAU;kDAAU;;;;;6DAE1B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;0CAKhC,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA5EwB;;QACiB,+IAAQ;;;KADzB", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { useTheme } from '@/contexts/ThemeContext';\r\n\r\nexport default function Footer() {\r\n  const { isDarkMode } = useTheme();\r\n  \r\n  return (\r\n    <footer className={`text-white mt-auto transition-colors duration-300 ${\r\n      isDarkMode ? 'bg-[#1e3a5f]' : 'bg-[#2d5a9e]'\r\n    }`}>\r\n      <div className=\"px-8 py-8\">\r\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center gap-8 w-full\">\r\n          {/* Logo and Copyright */}\r\n          <div className=\"flex flex-col gap-3\">\r\n            <Image \r\n              src=\"/Landing_Page/PIP LOGO 2.svg\"\r\n              alt=\"PIP FTUI Logo\"\r\n              width={220}\r\n              height={80}\r\n              className=\"h-20 w-auto\"\r\n            />\r\n            <p className=\"text-white font-[family-name:var(--font-comfortaa)] text-base font-semibold\">\r\n              @ PIP All Rights Reserved.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Address and Social Links */}\r\n          <div className=\"flex flex-col items-start md:items-end gap-4\">\r\n            {/* Address */}\r\n            <div className=\"flex items-start gap-2 text-white\">\r\n              <Image \r\n                src=\"/Footer/marker-pin-02.png\"\r\n                alt=\"Location Pin\"\r\n                width={20}\r\n                height={20}\r\n                className=\"mt-0.5 flex-shrink-0\"\r\n              />\r\n              <p className=\"text-sm font-[family-name:var(--font-comfortaa)] max-w-md text-left md:text-right leading-relaxed\">\r\n                Pusgiwa UI, Gedung D Lt. 7, Jl. Prof. Dr. Fuad Hassan, Kukusan, Kecamatan Beji, Kota Depok, Jawa Barat 16425\r\n              </p>\r\n            </div>\r\n\r\n            {/* Social Media Icons */}\r\n            <div className=\"flex items-center gap-3\">\r\n              <Link \r\n                href=\"https://instagram.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"Instagram\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/instagram 1.png\"\r\n                  alt=\"Instagram\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n              <Link \r\n                href=\"https://linkedin.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"LinkedIn\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/linkedin 1.png\"\r\n                  alt=\"LinkedIn\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n              <Link \r\n                href=\"https://youtube.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"YouTube\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/youtube 1.png\"\r\n                  alt=\"YouTube\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n              <Link \r\n                href=\"https://facebook.com\" \r\n                target=\"_blank\"\r\n                className=\"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity\"\r\n                aria-label=\"Facebook\"\r\n              >\r\n                <Image \r\n                  src=\"/Footer/facebook 1.png\"\r\n                  alt=\"Facebook\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"w-full h-full\"\r\n                />\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,+IAAQ;IAE/B,qBACE,6LAAC;QAAO,WAAW,AAAC,qDAEnB,OADC,aAAa,iBAAiB;kBAE9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2IAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,6LAAC;gCAAE,WAAU;0CAA8E;;;;;;;;;;;;kCAM7F,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2IAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAAoG;;;;;;;;;;;;0CAMnH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAGd,6LAAC,0KAAI;wCACH,MAAK;wCACL,QAAO;wCACP,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B;GAvGwB;;QACC,+IAAQ;;;KADT", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/components/RAGPage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport Navbar from './Navbar';\r\nimport Footer from './Footer';\r\n\r\ninterface Message {\r\n  id: string;\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  sources?: string[];\r\n}\r\n\r\ninterface Document {\r\n  name: string;\r\n}\r\n\r\nexport default function RAGPage() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [documents, setDocuments] = useState<Document[]>([]);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [showUpload, setShowUpload] = useState(false);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  useEffect(() => {\r\n    fetchDocuments();\r\n  }, []);\r\n\r\n  const fetchDocuments = async () => {\r\n    try {\r\n      const response = await fetch('/api/documents');\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setDocuments(data.documents.map((name: string) => ({ name })));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching documents:', error);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    setIsUploading(true);\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    try {\r\n      const response = await fetch('/api/documents', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        await fetchDocuments();\r\n        alert('Document uploaded successfully!');\r\n        setShowUpload(false);\r\n      } else {\r\n        const error = await response.json();\r\n        alert(`Upload failed: ${error.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error uploading file:', error);\r\n      alert('Failed to upload document');\r\n    } finally {\r\n      setIsUploading(false);\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = '';\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() || isLoading) return;\r\n\r\n    const userMessage: Message = {\r\n      id: Date.now().toString(),\r\n      role: 'user',\r\n      content: input,\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await fetch('/api/rag', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          question: input,\r\n          top_k: 3,\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch response');\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      const assistantMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: data.answer,\r\n        sources: data.sources,\r\n      };\r\n\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n    } catch (error) {\r\n      console.error('Error:', error);\r\n      const errorMessage: Message = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: 'Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running.',\r\n      };\r\n      setMessages(prev => [...prev, errorMessage]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\" style={{ background: 'linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)' }}>\r\n      <Navbar />\r\n      \r\n      <div className=\"mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-8\">\r\n          <div>\r\n            <h1 className=\"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]\">\r\n              PIP FTUI - RAG Mode\r\n            </h1>\r\n            <h2 className=\"text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]\">\r\n              Retrieval-Augmented Generation\r\n            </h2>\r\n          </div>\r\n          <p className=\"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]\">\r\n            Cari informasi FTUI berdasarkan dokumen yang tersedia\r\n          </p>\r\n          {/* Mode Switcher */}\r\n          <div className=\"flex justify-center gap-4 mt-6\">\r\n            <Link href=\"/chat\">\r\n              <div className=\"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n                💬 Chat Mode\r\n              </div>\r\n            </Link>\r\n            <div className=\"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]\">\r\n              📚 RAG Mode\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex gap-6\">\r\n          {/* Sidebar - Documents */}\r\n          <div className=\"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h2 className=\"text-xl font-bold text-gray-800\">Documents</h2>\r\n              <button\r\n                onClick={() => setShowUpload(!showUpload)}\r\n                className=\"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\r\n              >\r\n                {showUpload ? 'Cancel' : '+ Upload'}\r\n              </button>\r\n            </div>\r\n\r\n            {showUpload && (\r\n              <div className=\"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300\">\r\n                <input\r\n                  ref={fileInputRef}\r\n                  type=\"file\"\r\n                  onChange={handleFileUpload}\r\n                  disabled={isUploading}\r\n                  accept=\".pdf,.txt,.docx,.md,.html\"\r\n                  className=\"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer\"\r\n                />\r\n                {isUploading && (\r\n                  <p className=\"text-sm text-purple-600 mt-2\">Uploading...</p>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"overflow-y-auto h-[calc(100%-100px)]\">\r\n              {documents.length === 0 ? (\r\n                <div className=\"text-center text-gray-500 mt-8\">\r\n                  <div className=\"text-4xl mb-2\">📄</div>\r\n                  <p className=\"text-sm\">No documents yet</p>\r\n                  <p className=\"text-xs text-gray-400 mt-1\">Upload a document to start</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  {documents.map((doc, idx) => (\r\n                    <div\r\n                      key={idx}\r\n                      className=\"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors\"\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <span className=\"text-2xl\">📄</span>\r\n                        <span className=\"text-sm text-gray-700 truncate flex-1\">\r\n                          {doc.name}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Main Chat Area */}\r\n          <div className=\"flex-1\">\r\n            {/* Chat Messages */}\r\n            <div ref={chatContainerRef} className=\"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200\">\r\n              <div className=\"p-6 space-y-6\">\r\n                {messages.length === 0 ? (\r\n                  <div className=\"text-center text-[#346ad5] mt-8\">\r\n                    <div className=\"text-6xl mb-4\">📚</div>\r\n                    <p className=\"text-lg font-semibold\">Tanyakan tentang informasi FTUI</p>\r\n                    <p className=\"text-sm\">Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan.</p>\r\n                  </div>\r\n                ) : (\r\n                  messages.map((message) => (\r\n                    <div\r\n                      key={message.id}\r\n                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n                    >\r\n                      <div\r\n                        className={`\r\n                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md\r\n                          ${message.role === 'user'\r\n                            ? 'bg-[#346ad5] text-white'\r\n                            : 'bg-white text-gray-800 border border-gray-200'}\r\n                        `}\r\n                      >\r\n                        <div className={`text-xs mb-2 font-medium ${\r\n                          message.role === 'user' ? 'text-blue-100' : 'text-[#346ad5]'\r\n                        }`}>\r\n                          {message.role === 'user' ? 'You' : 'PIP RAG Assistant'}\r\n                        </div>\r\n                        <div className={`text-sm leading-relaxed whitespace-pre-wrap ${\r\n                          message.role === 'user' ? 'text-white' : 'text-gray-700'\r\n                        }`}>\r\n                          {message.content}\r\n                        </div>\r\n                        {message.sources && message.sources.length > 0 && (\r\n                          <div className=\"mt-4 pt-4 border-t border-gray-300\">\r\n                            <p className=\"text-xs font-semibold text-gray-600 mb-2\">Sources:</p>\r\n                            <div className=\"space-y-2\">\r\n                              {message.sources.map((source, idx) => (\r\n                                <div key={idx} className=\"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200\">\r\n                                  {source}\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                )}\r\n                {isLoading && (\r\n                  <div className=\"flex justify-start\">\r\n                    <div className=\"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200\">\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <div className=\"flex space-x-1\">\r\n                          <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\"></div>\r\n                          <div className=\"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                          <div className=\"w-2 h-2 bg-[#fae664] rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                        </div>\r\n                        <span className=\"text-sm text-[#346ad5]\">Mencari di dokumen...</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Input Form */}\r\n            <form onSubmit={handleSubmit} className=\"relative flex-shrink-0\">\r\n              <div className=\"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4\">\r\n                <input\r\n                  value={input}\r\n                  onChange={(e) => setInput(e.target.value)}\r\n                  placeholder=\"ASK PIP...\"\r\n                  disabled={isLoading || documents.length === 0}\r\n                  className=\"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed\"\r\n                />\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={isLoading || !input.trim() || documents.length === 0}\r\n                  className=\"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]\"\r\n                  aria-label=\"Send message\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAa,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,mBAAmB,IAAA,uKAAM,EAAiB;IAChD,MAAM,eAAe,IAAA,uKAAM,EAAmB;IAE9C,MAAM,iBAAiB;QACrB,IAAI,iBAAiB,OAAO,EAAE;YAC5B,iBAAiB,OAAO,CAAC,SAAS,GAAG,iBAAiB,OAAO,CAAC,YAAY;QAC5E;IACF;IAEA,IAAA,0KAAS;6BAAC;YACR;QACF;4BAAG;QAAC;KAAS;IAEb,IAAA,0KAAS;6BAAC;YACR;QACF;4BAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,OAAiB,CAAC;wBAAE;oBAAK,CAAC;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,mBAAmB,OAAO;YACjB;QAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM;QAEX,eAAe;QACf,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM;gBACN,cAAc;YAChB,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,kBAA6B,OAAZ,MAAM,KAAK;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,eAAe;YACf,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;QACX;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,YAAY;gBACvC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;oBACV,OAAO;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,mBAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,KAAK,MAAM;gBACpB,SAAS,KAAK,OAAO;YACvB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,UAAU;YACxB,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;YACX;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAA6B,OAAO;YAAE,YAAY;QAAiE;;0BAChI,6LAAC,0IAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA8E;;;;;;kDAG5F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;0CAIjG,6LAAC;gCAAE,WAAU;0CAA2E;;;;;;0CAIxF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0KAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC;4CAAI,WAAU;sDAAsK;;;;;;;;;;;kDAIvL,6LAAC;wCAAI,WAAU;kDAA8G;;;;;;;;;;;;;;;;;;kCAMjI,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,6LAAC;gDACC,SAAS,IAAM,cAAc,CAAC;gDAC9B,WAAU;0DAET,aAAa,WAAW;;;;;;;;;;;;oCAI5B,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK;gDACL,MAAK;gDACL,UAAU;gDACV,UAAU;gDACV,QAAO;gDACP,WAAU;;;;;;4CAEX,6BACC,6LAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;kDAKlD,6LAAC;wCAAI,WAAU;kDACZ,UAAU,MAAM,KAAK,kBACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;iEAG5C,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,KAAK,oBACnB,6LAAC;oDAEC,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAW;;;;;;0EAC3B,6LAAC;gEAAK,WAAU;0EACb,IAAI,IAAI;;;;;;;;;;;;mDANR;;;;;;;;;;;;;;;;;;;;;0CAiBjB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,KAAK;wCAAkB,WAAU;kDACpC,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAU;;;;;;;;;;;2DAGzB,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;wDAEC,WAAW,AAAC,QAAiE,OAA1D,QAAQ,IAAI,KAAK,SAAS,gBAAgB;kEAE7D,cAAA,6LAAC;4DACC,WAAW,AAAC,sGAI0C,OAFlD,QAAQ,IAAI,KAAK,SACf,4BACA,iDAAgD;;8EAGtD,6LAAC;oEAAI,WAAW,AAAC,4BAEhB,OADC,QAAQ,IAAI,KAAK,SAAS,kBAAkB;8EAE3C,QAAQ,IAAI,KAAK,SAAS,QAAQ;;;;;;8EAErC,6LAAC;oEAAI,WAAW,AAAC,+CAEhB,OADC,QAAQ,IAAI,KAAK,SAAS,eAAe;8EAExC,QAAQ,OAAO;;;;;;gEAEjB,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAA2C;;;;;;sFACxD,6LAAC;4EAAI,WAAU;sFACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,oBAC5B,6LAAC;oFAAc,WAAU;8FACtB;mFADO;;;;;;;;;;;;;;;;;;;;;;uDA1Bf,QAAQ,EAAE;;;;;gDAqCpB,2BACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;4EAAmD,OAAO;gFAAE,gBAAgB;4EAAO;;;;;;sFAClG,6LAAC;4EAAI,WAAU;4EAAmD,OAAO;gFAAE,gBAAgB;4EAAO;;;;;;;;;;;;8EAEpG,6LAAC;oEAAK,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASrD,6LAAC;wCAAK,UAAU;wCAAc,WAAU;kDACtC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,aAAY;oDACZ,UAAU,aAAa,UAAU,MAAM,KAAK;oDAC5C,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,UAAU,aAAa,CAAC,MAAM,IAAI,MAAM,UAAU,MAAM,KAAK;oDAC7D,WAAU;oDACV,cAAW;8DAEX,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,6LAAC,0IAAM;;;;;;;;;;;AAGb;GAlTwB;KAAA", "debugId": null}}]}