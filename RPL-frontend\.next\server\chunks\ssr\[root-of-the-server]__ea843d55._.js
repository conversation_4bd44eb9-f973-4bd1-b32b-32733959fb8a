module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},62397,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(87924),c=a.i(72131),d=a.i(73885),e=a.i(56283),f=a.i(71987),g=a.i(9453);function h(){let{isDarkMode:a}=(0,g.useTheme)(),[h,i]=(0,c.useState)([]),[j,k]=(0,c.useState)(""),[l,m]=(0,c.useState)(!1);(0,c.useRef)(null);let n=(0,c.useRef)(null);(0,c.useEffect)(()=>{n.current&&(n.current.scrollTop=n.current.scrollHeight)},[h]);let o=async a=>{if(a.preventDefault(),!j.trim()||l)return;let b={id:Date.now().toString(),role:"user",content:j,timestamp:new Date().toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})};i(a=>[...a,b]),k(""),m(!0),setTimeout(()=>{let a={id:(Date.now()+1).toString(),role:"assistant",content:"This is a placeholder response from PIP FTUI assistant. The actual RAG functionality is in development.",timestamp:new Date().toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})};i(b=>[...b,a]),m(!1)},1e3)};return(0,b.jsxs)("div",{className:"min-h-screen flex flex-col relative overflow-x-hidden",children:[(0,b.jsxs)("div",{className:"fixed inset-0 w-full h-full -z-10",children:[(0,b.jsx)("div",{className:`absolute inset-0 transition-colors duration-300 ${a?"bg-transparent":"bg-white"}`}),(0,b.jsx)("div",{className:`absolute inset-0 transition-all duration-300 ${a?"brightness-[0.4]":""}`,children:(0,b.jsx)(f.default,{src:"/Home_Page/Backround Design.svg",alt:"Background with yellow circles",fill:!0,className:"object-cover",priority:!0,quality:100})})]}),(0,b.jsx)(d.default,{}),(0,b.jsx)("div",{className:"flex flex-col pt-20 pb-4 px-8 min-h-screen",children:(0,b.jsxs)("div",{className:"w-full flex flex-col gap-4 mx-auto mb-4",style:{maxWidth:"calc(100% - 4rem)",minHeight:"calc(100vh - 9rem)"},children:[(0,b.jsx)("div",{className:`flex-1 backdrop-blur-sm rounded-[32px] shadow-[0_8px_32px_rgba(0,0,0,0.3)] p-8 overflow-hidden transition-colors duration-300 ${a?"bg-gradient-to-br from-[#1e3a5f]/90 via-[#2d4a6e]/90 to-[#3d5a7e]/90":"bg-gradient-to-br from-[#5a6c7d]/90 via-[#5a7a9d]/90 to-[#4a6b8a]/90"}`,children:(0,b.jsxs)("div",{ref:n,className:"h-full overflow-y-auto space-y-4 pr-2 chat-scroll",children:[0===h.length?(0,b.jsx)("div",{className:"flex items-center justify-center h-full text-white/60 text-center",children:(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"💬"}),(0,b.jsx)("p",{className:"text-xl font-[family-name:var(--font-comfortaa)]",children:"Ask PIP..."})]})}):h.map(a=>(0,b.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`max-w-[75%] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)] ${"user"===a.role?"bg-[#f5e6a3] text-gray-900":"bg-[#e8eef5] text-gray-900"}`,children:[(0,b.jsx)("div",{className:"text-[15px] leading-relaxed whitespace-pre-wrap font-[family-name:var(--font-quicksand)]",children:a.content}),a.timestamp&&(0,b.jsx)("div",{className:"text-[11px] text-gray-500 mt-2 text-right font-[family-name:var(--font-quicksand)]",children:a.timestamp}),a.attachments&&a.attachments.length>0&&(0,b.jsx)("div",{className:"mt-3 space-y-2",children:a.attachments.map((a,c)=>(0,b.jsxs)("div",{className:"bg-yellow-400 rounded-lg p-3 flex items-center gap-3",children:[(0,b.jsx)("div",{className:"text-3xl",children:"📄"}),(0,b.jsxs)("div",{className:"flex-grow",children:[(0,b.jsx)("p",{className:"font-semibold text-sm",children:a.name}),(0,b.jsx)("p",{className:"text-xs text-gray-600",children:a.size})]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)("button",{className:"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium",children:"View File"}),(0,b.jsx)("button",{className:"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium",children:"Download File"})]})]},c))})]})},a.id)),l&&(0,b.jsx)("div",{className:"flex justify-start",children:(0,b.jsx)("div",{className:"bg-[#e8eef5] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)]",children:(0,b.jsx)("div",{className:"flex items-center space-x-2",children:(0,b.jsxs)("div",{className:"flex space-x-1",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce"}),(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,b.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})})})]})}),(0,b.jsx)("form",{onSubmit:o,className:"relative flex-shrink-0",children:(0,b.jsxs)("div",{className:`rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4 transition-colors duration-300 ${a?"bg-gradient-to-r from-[#1e3a5f] via-[#2d4a6e] to-[#3d5a7e]":"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a]"}`,children:[(0,b.jsx)("input",{value:j,onChange:a=>k(a.target.value),placeholder:"ASK PIP...",disabled:l,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,b.jsx)("button",{type:"submit",disabled:l||!j.trim(),className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]})}),(0,b.jsx)(e.default,{})]})}},20892,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(87924),c=a.i(72131),d=a.i(38246),e=a.i(73885),f=a.i(56283);function g(){let[a,g]=(0,c.useState)([]),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(!1),l=(0,c.useRef)(null);(0,c.useEffect)(()=>{l.current&&(l.current.scrollTop=l.current.scrollHeight)},[a]);let m=async b=>{if(b.preventDefault(),!h.trim()||j)return;let c={id:Date.now().toString(),role:"user",content:h};g(a=>[...a,c]),i(""),k(!0);let d={id:(Date.now()+1).toString(),role:"assistant",content:""};g(a=>[...a,d]);try{let b=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...a,c]})});if(!b.ok)throw Error("Failed to fetch response");let e=b.body?.getReader(),f=new TextDecoder;if(e)for(;;){let{done:a,value:b}=await e.read();if(a)break;for(let a of f.decode(b).split("\n"))if(a.startsWith("data: ")){let b=a.slice(6);if("[DONE]"===b)break;try{let a=JSON.parse(b);a.content&&g(b=>b.map(b=>b.id===d.id?{...b,content:b.content+a.content}:b))}catch(a){}}}}catch(a){console.error("Error:",a),g(a=>a.map(a=>a.id===d.id?{...a,content:"Sorry, I encountered an error. Please try again."}:a))}finally{k(!1)}};return(0,b.jsxs)("div",{className:"min-h-screen flex flex-col",style:{background:"linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"},children:[(0,b.jsx)(e.default,{}),(0,b.jsxs)("div",{className:"mx-auto w-full max-w-4xl py-8 px-4 mt-24 flex-grow",children:[(0,b.jsxs)("div",{className:"text-center mb-8",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]",children:"Pusat Informasi Publik"}),(0,b.jsx)("h2",{className:"text-2xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]",children:"Fakultas Teknik Universitas Indonesia"})]}),(0,b.jsx)("p",{className:"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]",children:"AI Assistant untuk Informasi dan Layanan FTUI"}),(0,b.jsxs)("div",{className:"flex justify-center gap-4 mt-6",children:[(0,b.jsx)("div",{className:"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]",children:"💬 Chat Mode"}),(0,b.jsx)(d.default,{href:"/rag",children:(0,b.jsx)("div",{className:"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]",children:"📚 RAG Mode"})})]})]}),(0,b.jsx)("div",{ref:l,className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,b.jsxs)("div",{className:"p-6 space-y-6",children:[0===a.length?(0,b.jsxs)("div",{className:"text-center text-[#346ad5] mt-8",children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"🏛️"}),(0,b.jsx)("p",{className:"text-lg font-semibold",children:"Selamat datang di PIP FTUI!"}),(0,b.jsx)("p",{className:"text-sm",children:"Silakan ajukan pertanyaan tentang Fakultas Teknik UI."})]}):a.map(a=>(0,b.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`
                      max-w-[80%] rounded-2xl px-6 py-4 shadow-md
                      ${"user"===a.role?"bg-[#346ad5] text-white":"bg-white text-gray-800 border border-gray-200"}
                    `,children:[(0,b.jsx)("div",{className:`text-xs mb-2 font-medium ${"user"===a.role?"text-blue-100":"text-[#346ad5]"}`,children:"user"===a.role?"You":"PIP Assistant"}),(0,b.jsx)("div",{className:`text-sm leading-relaxed whitespace-pre-wrap ${"user"===a.role?"text-white":"text-gray-700"}`,children:a.content})]})},a.id)),j&&(0,b.jsx)("div",{className:"flex justify-start",children:(0,b.jsx)("div",{className:"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)("div",{className:"flex space-x-1",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce"}),(0,b.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,b.jsx)("div",{className:"w-2 h-2 bg-[#fae664] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,b.jsx)("span",{className:"text-sm text-[#346ad5]",children:"PIP Assistant sedang berpikir..."})]})})})]})}),(0,b.jsx)("form",{onSubmit:m,className:"relative flex-shrink-0",children:(0,b.jsxs)("div",{className:"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4",children:[(0,b.jsx)("input",{value:h,onChange:a=>i(a.target.value),placeholder:"ASK PIP...",disabled:j,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,b.jsx)("button",{type:"submit",disabled:j||!h.trim(),className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]}),(0,b.jsx)(f.default,{})]})}},9317,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(87924),c=a.i(72131),d=a.i(38246),e=a.i(73885),f=a.i(56283);function g(){let[a,g]=(0,c.useState)([]),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(!1),[l,m]=(0,c.useState)([]),[n,o]=(0,c.useState)(!1),[p,q]=(0,c.useState)(!1),r=(0,c.useRef)(null),s=(0,c.useRef)(null);(0,c.useEffect)(()=>{r.current&&(r.current.scrollTop=r.current.scrollHeight)},[a]),(0,c.useEffect)(()=>{t()},[]);let t=async()=>{try{let a=await fetch("/api/documents");if(a.ok){let b=await a.json();m(b.documents.map(a=>({name:a})))}}catch(a){console.error("Error fetching documents:",a)}},u=async a=>{let b=a.target.files?.[0];if(!b)return;o(!0);let c=new FormData;c.append("file",b);try{let a=await fetch("/api/documents",{method:"POST",body:c});if(a.ok)await t(),alert("Document uploaded successfully!"),q(!1);else{let b=await a.json();alert(`Upload failed: ${b.error}`)}}catch(a){console.error("Error uploading file:",a),alert("Failed to upload document")}finally{o(!1),s.current&&(s.current.value="")}},v=async a=>{if(a.preventDefault(),!h.trim()||j)return;let b={id:Date.now().toString(),role:"user",content:h};g(a=>[...a,b]),i(""),k(!0);try{let a=await fetch("/api/rag",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:h,top_k:3})});if(!a.ok)throw Error("Failed to fetch response");let b=await a.json(),c={id:(Date.now()+1).toString(),role:"assistant",content:b.answer,sources:b.sources};g(a=>[...a,c])}catch(b){console.error("Error:",b);let a={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running."};g(b=>[...b,a])}finally{k(!1)}};return(0,b.jsxs)("div",{className:"min-h-screen flex flex-col",style:{background:"linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"},children:[(0,b.jsx)(e.default,{}),(0,b.jsxs)("div",{className:"mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow",children:[(0,b.jsxs)("div",{className:"text-center mb-8",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]",children:"PIP FTUI - RAG Mode"}),(0,b.jsx)("h2",{className:"text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]",children:"Retrieval-Augmented Generation"})]}),(0,b.jsx)("p",{className:"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]",children:"Cari informasi FTUI berdasarkan dokumen yang tersedia"}),(0,b.jsxs)("div",{className:"flex justify-center gap-4 mt-6",children:[(0,b.jsx)(d.default,{href:"/chat",children:(0,b.jsx)("div",{className:"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]",children:"💬 Chat Mode"})}),(0,b.jsx)("div",{className:"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]",children:"📚 RAG Mode"})]})]}),(0,b.jsxs)("div",{className:"flex gap-6",children:[(0,b.jsxs)("div",{className:"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,b.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Documents"}),(0,b.jsx)("button",{onClick:()=>q(!p),className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:p?"Cancel":"+ Upload"})]}),p&&(0,b.jsxs)("div",{className:"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300",children:[(0,b.jsx)("input",{ref:s,type:"file",onChange:u,disabled:n,accept:".pdf,.txt,.docx,.md,.html",className:"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer"}),n&&(0,b.jsx)("p",{className:"text-sm text-purple-600 mt-2",children:"Uploading..."})]}),(0,b.jsx)("div",{className:"overflow-y-auto h-[calc(100%-100px)]",children:0===l.length?(0,b.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,b.jsx)("div",{className:"text-4xl mb-2",children:"📄"}),(0,b.jsx)("p",{className:"text-sm",children:"No documents yet"}),(0,b.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Upload a document to start"})]}):(0,b.jsx)("div",{className:"space-y-2",children:l.map((a,c)=>(0,b.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors",children:(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)("span",{className:"text-2xl",children:"📄"}),(0,b.jsx)("span",{className:"text-sm text-gray-700 truncate flex-1",children:a.name})]})},c))})})]}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("div",{ref:r,className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,b.jsxs)("div",{className:"p-6 space-y-6",children:[0===a.length?(0,b.jsxs)("div",{className:"text-center text-[#346ad5] mt-8",children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"📚"}),(0,b.jsx)("p",{className:"text-lg font-semibold",children:"Tanyakan tentang informasi FTUI"}),(0,b.jsx)("p",{className:"text-sm",children:"Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan."})]}):a.map(a=>(0,b.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`
                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md
                          ${"user"===a.role?"bg-[#346ad5] text-white":"bg-white text-gray-800 border border-gray-200"}
                        `,children:[(0,b.jsx)("div",{className:`text-xs mb-2 font-medium ${"user"===a.role?"text-blue-100":"text-[#346ad5]"}`,children:"user"===a.role?"You":"PIP RAG Assistant"}),(0,b.jsx)("div",{className:`text-sm leading-relaxed whitespace-pre-wrap ${"user"===a.role?"text-white":"text-gray-700"}`,children:a.content}),a.sources&&a.sources.length>0&&(0,b.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-300",children:[(0,b.jsx)("p",{className:"text-xs font-semibold text-gray-600 mb-2",children:"Sources:"}),(0,b.jsx)("div",{className:"space-y-2",children:a.sources.map((a,c)=>(0,b.jsx)("div",{className:"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200",children:a},c))})]})]})},a.id)),j&&(0,b.jsx)("div",{className:"flex justify-start",children:(0,b.jsx)("div",{className:"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)("div",{className:"flex space-x-1",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce"}),(0,b.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,b.jsx)("div",{className:"w-2 h-2 bg-[#fae664] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,b.jsx)("span",{className:"text-sm text-[#346ad5]",children:"Mencari di dokumen..."})]})})})]})}),(0,b.jsx)("form",{onSubmit:v,className:"relative flex-shrink-0",children:(0,b.jsxs)("div",{className:"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4",children:[(0,b.jsx)("input",{value:h,onChange:a=>i(a.target.value),placeholder:"ASK PIP...",disabled:j||0===l.length,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,b.jsx)("button",{type:"submit",disabled:j||!h.trim()||0===l.length,className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]})]})]}),(0,b.jsx)(f.default,{})]})}},84245,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(87924),c=a.i(71987),d=a.i(72131),e=a.i(73885),f=a.i(56283),g=a.i(9453);function h(){let{isDarkMode:a}=(0,g.useTheme)(),[h,i]=(0,d.useState)({nama:"",email:"",subject:"",pesan:""}),j=a=>{i({...h,[a.target.name]:a.target.value})};return(0,b.jsxs)("div",{className:"min-h-screen flex flex-col relative overflow-x-hidden",children:[(0,b.jsxs)("div",{className:"fixed inset-0 w-full h-full -z-10",children:[(0,b.jsx)("div",{className:`absolute inset-0 transition-colors duration-300 ${a?"bg-transparent":"bg-white"}`}),(0,b.jsx)("div",{className:`absolute inset-0 transition-all duration-300 ${a?"brightness-[0.4]":""}`,children:(0,b.jsx)(c.default,{src:"/Home_Page/Backround Design.svg",alt:"Background with yellow circles",fill:!0,className:"object-cover",priority:!0,quality:100})})]}),(0,b.jsx)(e.default,{}),(0,b.jsx)("div",{className:"flex-grow pt-24 pb-8 px-8",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,b.jsxs)("div",{className:`rounded-3xl p-8 text-white shadow-2xl transition-colors duration-300 ${a?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"}`,children:[(0,b.jsx)("h2",{className:"text-2xl font-bold mb-6 font-[family-name:var(--font-comfortaa)]",children:"Faculty Contact Info"}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Head Office"}),(0,b.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung Dekanat FTUI Lt. 2",(0,b.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,b.jsx)("br",{}),"Kampus UI Depok",(0,b.jsx)("br",{}),"+6221 7863504, +6221 7863505"]})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Kantor Humas dan Protokol"}),(0,b.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung GK IPAJI lantai 1",(0,b.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,b.jsx)("br",{}),"Kampus UI Depok",(0,b.jsx)("br",{}),"+6221 78888430 ext 106",(0,b.jsx)("br",{}),"<EMAIL>"]})]}),(0,b.jsxs)("div",{className:"mb-6",children:[(0,b.jsx)("p",{className:"text-sm font-bold font-[family-name:var(--font-quicksand)]",children:"Mon – Fri 8:00A.M. – 4:00P.M."}),(0,b.jsx)("p",{className:"text-sm font-semibold font-[family-name:var(--font-quicksand)]",children:"Social Info"})]}),(0,b.jsxs)("div",{className:"flex gap-4",children:[(0,b.jsx)("a",{href:"https://instagram.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/instagram 1.png",alt:"Instagram",fill:!0,className:"object-contain"})})}),(0,b.jsx)("a",{href:"https://linkedin.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/linkedin 1.png",alt:"LinkedIn",fill:!0,className:"object-contain"})})}),(0,b.jsx)("a",{href:"https://youtube.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/youtube 1.png",alt:"YouTube",fill:!0,className:"object-contain"})})}),(0,b.jsx)("a",{href:"https://facebook.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,b.jsx)("div",{className:"w-8 h-8 relative",children:(0,b.jsx)(c.default,{src:"/Footer/facebook 1.png",alt:"Facebook",fill:!0,className:"object-contain"})})})]})]}),(0,b.jsx)("div",{className:`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${a?"bg-gradient-to-br from-[#2d4a6e] to-[#1e3a5f]":"bg-gradient-to-br from-[#7a8a9a] to-[#5a7a9d]"}`,children:(0,b.jsxs)("form",{onSubmit:a=>{a.preventDefault(),console.log("Form submitted:",h),alert("Pesan Anda telah terkirim!"),i({nama:"",email:"",subject:"",pesan:""})},className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Nama"}),(0,b.jsx)("input",{type:"text",name:"nama",value:h.nama,onChange:j,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan nama Anda"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Email"}),(0,b.jsx)("input",{type:"email",name:"email",value:h.email,onChange:j,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan email Anda"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Subject"}),(0,b.jsx)("input",{type:"text",name:"subject",value:h.subject,onChange:j,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan subjek"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Pesan"}),(0,b.jsx)("textarea",{name:"pesan",value:h.pesan,onChange:j,required:!0,rows:6,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] resize-none font-[family-name:var(--font-quicksand)]",placeholder:"Tulis pesan Anda"})]}),(0,b.jsx)("button",{type:"submit",className:"w-full bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 font-bold py-3 rounded-xl transition-all shadow-lg font-[family-name:var(--font-comfortaa)]",children:"Kirim Pesan"})]})})]}),(0,b.jsxs)("div",{className:`rounded-3xl p-8 shadow-2xl transition-colors duration-300 ${a?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"}`,children:[(0,b.jsx)("h2",{className:"text-2xl font-bold mb-6 text-white font-[family-name:var(--font-comfortaa)]",children:"Departement Contact Info"}),(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>",telp:"WA 7270042 - 78849042",whatsapp:"WA 081212776702 (sekretariat Teknik Mesin)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Elektro",email:"<EMAIL>",telp:"+6221 7270078 - 7863504",whatsapp:"081289606440"},{name:"Departemen Teknik Metalurgi",email:"<EMAIL>",telp:"+6221 78849044",whatsapp:"081519996009"},{name:"Departemen Teknik Kimia",email:"<EMAIL>",telp:"+6221 7863516",whatsapp:"082112025025"},{name:"Departemen Arsitektur",email:"<EMAIL>",telp:"+6221 7270062",whatsapp:"081296661126"},{name:"Departemen Teknik Industri",email:"<EMAIL>",telp:"+6221 7270041",whatsapp:"082112345678"},{name:"Departemen Teknik Komputer",email:"<EMAIL>",telp:"+6221 7863512",whatsapp:"081234567890"}].map((a,c)=>(0,b.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl transition-shadow",children:[(0,b.jsx)("h3",{className:"text-[#2d5a9e] font-bold text-sm mb-3 font-[family-name:var(--font-comfortaa)]",children:a.name}),(0,b.jsxs)("div",{className:"space-y-2 text-xs font-[family-name:var(--font-quicksand)]",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-semibold text-gray-700",children:"Email"}),(0,b.jsx)("p",{className:"text-gray-600",children:a.email})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-semibold text-gray-700",children:"Telp"}),(0,b.jsx)("p",{className:"text-gray-600",children:a.telp})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-semibold text-gray-700",children:"Whatsapp Only"}),(0,b.jsx)("p",{className:"text-gray-600",children:a.whatsapp})]})]})]},c))})]})]})}),(0,b.jsx)(f.default,{})]})}},75776,a=>{"use strict";a.s(["default",()=>e]);var b=a.i(87924),c=a.i(71987),d=a.i(38246);function e(){return(0,b.jsxs)("div",{className:"min-h-screen flex items-center justify-center relative overflow-hidden",children:[(0,b.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,b.jsx)(c.default,{src:"/Landing_Page/Background.png",alt:"FTUI Campus Background",fill:!0,className:"object-cover",priority:!0,quality:100}),(0,b.jsx)("div",{className:"absolute inset-0 bg-[#4a6b8a]/70"})]}),(0,b.jsxs)("div",{className:"relative z-10 text-center px-4 flex flex-col items-center",children:[(0,b.jsx)("div",{className:"mb-8 flex justify-center",children:(0,b.jsx)("div",{className:"relative",children:(0,b.jsx)(c.default,{src:"/Landing_Page/PIP LOGO 2.svg",alt:"PIP FTUI Logo",width:700,height:250,className:"drop-shadow-2xl w-full max-w-[700px] h-auto",priority:!0})})}),(0,b.jsx)(d.default,{href:"/home",children:(0,b.jsx)("div",{className:"inline-block relative group cursor-pointer mt-4",children:(0,b.jsxs)("div",{className:"relative w-[180px] h-[65px]",children:[(0,b.jsx)(c.default,{src:"/Landing_Page/Button.svg",alt:"Dive In Button",width:180,height:65,className:"transition-transform duration-300 group-hover:scale-105 drop-shadow-lg"}),(0,b.jsx)("div",{className:"absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none",children:(0,b.jsx)("span",{className:"text-[#ffffff] text-[22px] font-bold font-[family-name:var(--font-comfortaa)] tracking-wide drop-shadow-sm",children:"Dive In"})})]})})})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ea843d55._.js.map