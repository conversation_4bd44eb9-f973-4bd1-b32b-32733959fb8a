# PIP FTUI - Integrasi LlamaIndex RAG dengan Next.js

Aplikasi chat dengan dua mode:
1. **Chat Mode**: Chat langsung dengan Llama 3.1 via Groq API
2. **RAG Mode**: Chat dengan dokumen Anda menggunakan LlamaIndex + Groq

## Arsitektur

```
┌─────────────────────────────────────────────────┐
│         Frontend (Next.js)                       │
│  - Chat Mode: /                                  │
│  - RAG Mode: /rag                                │
└─────────────────┬───────────────────────────────┘
                  │
                  │ HTTP API Calls
                  │
                  ├──────────────┐
                  │              │
                  ▼              ▼
         ┌────────────┐  ┌──────────────┐
         │   Groq     │  │   Backend    │
         │    API     │  │   Python     │
         │  (Direct)  │  │  (FastAPI)   │
         └────────────┘  └──────┬───────┘
                                │
                                ▼
                         ┌──────────────┐
                         │  LlamaIndex  │
                         │   + Groq     │
                         │  + Vector DB │
                         └──────────────┘
```

## Setup Lengkap

### 1. Setup Backend Python

```bash
# Masuk ke folder backend
cd backend

# Install dependencies
pip install -r requirements.txt

# Copy dan edit .env
cp .env.example .env
# Edit .env dan isi GROQ_API_KEY Anda

# Jalankan server
python main.py
```

Backend akan berjalan di: **http://localhost:8000**

### 2. Setup Frontend Next.js

```bash
# Masuk ke folder frontend
cd my-groq-app

# Install dependencies (jika belum)
npm install

# Copy dan edit environment variables
cp .env.local.example .env.local
# Edit .env.local:
# - NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
# - GROQ_API_KEY=your_groq_api_key_here

# Jalankan development server
npm run dev
```

Frontend akan berjalan di: **http://localhost:3000**

### 3. Dapatkan Groq API Key

1. Kunjungi: https://console.groq.com/
2. Sign up / Login
3. Buat API key baru
4. Copy API key dan paste ke file `.env` (backend) dan `.env.local` (frontend)

## Cara Menggunakan

### Chat Mode (/)

1. Buka http://localhost:3000
2. Ketik pertanyaan apapun
3. AI akan menjawab menggunakan Llama 3.1 8B Instant

### RAG Mode (/rag)

1. Klik tombol "📚 RAG Mode" atau buka http://localhost:3000/rag
2. **Upload Dokumen**:
   - Klik tombol "+ Upload"
   - Pilih file (PDF, TXT, DOCX, MD, HTML)
   - Tunggu hingga upload selesai
3. **Tanya Dokumen**:
   - Ketik pertanyaan tentang dokumen Anda
   - AI akan mencari di dokumen dan menjawab dengan konteks
   - Lihat sumber jawaban di bagian "Sources"

## Fitur

### Chat Mode
- ✅ Streaming response real-time
- ✅ Context-aware conversation
- ✅ Fast responses with Groq
- ✅ Clean UI dengan Tailwind CSS

### RAG Mode
- ✅ Upload multiple documents
- ✅ Vector search dengan LlamaIndex
- ✅ Answer dengan source citations
- ✅ Support berbagai format dokumen
- ✅ Persistent index storage
- ✅ Document management (list, delete)

## API Endpoints

### Frontend Routes
- `GET /` - Chat mode interface
- `GET /rag` - RAG mode interface
- `POST /api/chat` - Proxy untuk Groq API (streaming)
- `POST /api/rag` - Query RAG backend
- `POST /api/documents` - Upload dokumen
- `GET /api/documents` - List dokumen

### Backend Routes
- `GET /health` - Health check
- `POST /api/upload` - Upload dokumen ke RAG
- `POST /api/query` - Query dokumen
- `GET /api/documents` - List semua dokumen
- `DELETE /api/documents/{filename}` - Hapus dokumen
- `POST /api/rebuild-index` - Rebuild vector index

## Troubleshooting

### Backend tidak bisa connect
```bash
# Cek apakah backend running
curl http://localhost:8000/health

# Jika tidak, restart backend
cd backend
python main.py
```

### Frontend tidak bisa query RAG
1. Pastikan backend running di port 8000
2. Cek `.env.local` di frontend:
   ```
   NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
   ```
3. Pastikan sudah upload dokumen terlebih dahulu

### Upload dokumen gagal
1. Cek ukuran file (max biasanya 100MB)
2. Pastikan format file didukung
3. Lihat logs di terminal backend

### Error: No module named 'torch'
```bash
# Install torch secara manual
pip install torch --index-url https://download.pytorch.org/whl/cpu
```

### CORS Error
Pastikan backend sudah set CORS dengan benar:
```python
# Di main.py, pastikan ada:
allow_origins=["http://localhost:3000"]
```

## Development

### Folder Structure

```
.
├── backend/                 # Python Backend
│   ├── main.py             # FastAPI app
│   ├── requirements.txt    # Python deps
│   ├── .env                # Environment vars
│   ├── data/               # Uploaded documents
│   └── storage/            # Vector index storage
│
└── my-groq-app/            # Next.js Frontend
    ├── src/
    │   ├── app/
    │   │   ├── page.tsx           # Chat mode
    │   │   ├── rag/page.tsx       # RAG mode
    │   │   └── api/
    │   │       ├── chat/route.ts      # Chat API
    │   │       ├── rag/route.ts       # RAG API
    │   │       └── documents/route.ts # Docs API
    │   └── ...
    ├── .env.local          # Frontend env vars
    └── package.json
```

## Tech Stack

### Frontend
- Next.js 14
- React 18
- TypeScript
- Tailwind CSS

### Backend
- Python 3.10+
- FastAPI
- LlamaIndex
- Groq SDK
- HuggingFace Embeddings

## Tips

1. **Untuk dokumen besar**: Gunakan chunking strategy yang tepat
2. **Untuk response lebih akurat**: Upload dokumen yang relevan saja
3. **Untuk speed**: Backend bisa di-deploy ke server terpisah
4. **Untuk production**: Gunakan proper vector database (Pinecone, Weaviate, dll)

## Next Steps

- [ ] Add authentication
- [ ] Add document preview
- [ ] Add chat history persistence
- [ ] Add multiple vector stores
- [ ] Deploy to production
- [ ] Add analytics

## License

MIT
