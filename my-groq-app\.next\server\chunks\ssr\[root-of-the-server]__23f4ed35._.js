module.exports=[72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},65329,a=>{"use strict";a.s(["default",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(38246);function e(){let[a,e]=(0,c.useState)([]),[f,g]=(0,c.useState)(""),[h,i]=(0,c.useState)(!1),[j,k]=(0,c.useState)([]),[l,m]=(0,c.useState)(!1),[n,o]=(0,c.useState)(!1),p=(0,c.useRef)(null),q=(0,c.useRef)(null);(0,c.useEffect)(()=>{p.current?.scrollIntoView({behavior:"smooth"})},[a]),(0,c.useEffect)(()=>{r()},[]);let r=async()=>{try{let a=await fetch("/api/documents");if(a.ok){let b=await a.json();k(b.documents.map(a=>({name:a})))}}catch(a){console.error("Error fetching documents:",a)}},s=async a=>{let b=a.target.files?.[0];if(!b)return;m(!0);let c=new FormData;c.append("file",b);try{let a=await fetch("/api/documents",{method:"POST",body:c});if(a.ok)await r(),alert("Document uploaded successfully!"),o(!1);else{let b=await a.json();alert(`Upload failed: ${b.error}`)}}catch(a){console.error("Error uploading file:",a),alert("Failed to upload document")}finally{m(!1),q.current&&(q.current.value="")}},t=async a=>{if(a.preventDefault(),!f.trim()||h)return;let b={id:Date.now().toString(),role:"user",content:f};e(a=>[...a,b]),g(""),i(!0);try{let a=await fetch("/api/rag",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:f,top_k:3})});if(!a.ok)throw Error("Failed to fetch response");let b=await a.json(),c={id:(Date.now()+1).toString(),role:"assistant",content:b.answer,sources:b.sources};e(a=>[...a,c])}catch(b){console.error("Error:",b);let a={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running."};e(b=>[...b,a])}finally{i(!1)}};return(0,b.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100",children:(0,b.jsxs)("div",{className:"mx-auto w-full max-w-6xl py-8 px-4",children:[(0,b.jsxs)("div",{className:"text-center mb-8",children:[(0,b.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"PIP FTUI - RAG Mode"}),(0,b.jsx)("p",{className:"text-gray-600 mb-4",children:"Chat with your documents using LlamaIndex & Groq"}),(0,b.jsxs)("div",{className:"flex justify-center gap-4 mt-4",children:[(0,b.jsx)(d.default,{href:"/",children:(0,b.jsx)("div",{className:"bg-white text-gray-700 border-2 border-gray-300 px-6 py-2 rounded-lg font-medium hover:border-blue-500 hover:text-blue-600 transition-colors cursor-pointer",children:"💬 Chat Mode"})}),(0,b.jsx)("div",{className:"bg-purple-500 text-white px-6 py-2 rounded-lg font-medium",children:"📚 RAG Mode"})]})]}),(0,b.jsxs)("div",{className:"flex gap-6",children:[(0,b.jsxs)("div",{className:"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,b.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Documents"}),(0,b.jsx)("button",{onClick:()=>o(!n),className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:n?"Cancel":"+ Upload"})]}),n&&(0,b.jsxs)("div",{className:"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300",children:[(0,b.jsx)("input",{ref:q,type:"file",onChange:s,disabled:l,accept:".pdf,.txt,.docx,.md,.html",className:"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer"}),l&&(0,b.jsx)("p",{className:"text-sm text-purple-600 mt-2",children:"Uploading..."})]}),(0,b.jsx)("div",{className:"overflow-y-auto h-[calc(100%-100px)]",children:0===j.length?(0,b.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,b.jsx)("div",{className:"text-4xl mb-2",children:"📄"}),(0,b.jsx)("p",{className:"text-sm",children:"No documents yet"}),(0,b.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Upload a document to start"})]}):(0,b.jsx)("div",{className:"space-y-2",children:j.map((a,c)=>(0,b.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors",children:(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)("span",{className:"text-2xl",children:"📄"}),(0,b.jsx)("span",{className:"text-sm text-gray-700 truncate flex-1",children:a.name})]})},c))})})]}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("div",{className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,b.jsxs)("div",{className:"p-6 space-y-6",children:[0===a.length?(0,b.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"🤖"}),(0,b.jsx)("p",{className:"text-lg",children:"Ask questions about your documents!"}),(0,b.jsx)("p",{className:"text-sm",children:"Upload documents and start asking questions."})]}):a.map(a=>(0,b.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`
                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md
                          ${"user"===a.role?"bg-gradient-to-r from-purple-500 to-pink-600 text-white":"bg-gray-100 text-gray-800 border border-gray-200"}
                        `,children:[(0,b.jsx)("div",{className:`text-xs mb-2 font-medium ${"user"===a.role?"text-purple-100":"text-gray-500"}`,children:"user"===a.role?"You":"RAG Assistant"}),(0,b.jsx)("div",{className:`text-sm leading-relaxed whitespace-pre-wrap ${"user"===a.role?"text-white":"text-gray-700"}`,children:a.content}),a.sources&&a.sources.length>0&&(0,b.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-300",children:[(0,b.jsx)("p",{className:"text-xs font-semibold text-gray-600 mb-2",children:"Sources:"}),(0,b.jsx)("div",{className:"space-y-2",children:a.sources.map((a,c)=>(0,b.jsx)("div",{className:"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200",children:a},c))})]})]})},a.id)),h&&(0,b.jsx)("div",{className:"flex justify-start",children:(0,b.jsx)("div",{className:"bg-gray-100 rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)("div",{className:"flex space-x-1",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce"}),(0,b.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,b.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,b.jsx)("span",{className:"text-sm text-gray-500",children:"Searching documents..."})]})})}),(0,b.jsx)("div",{ref:p})]})}),(0,b.jsxs)("form",{onSubmit:t,className:"flex gap-4",children:[(0,b.jsx)("div",{className:"flex-1 relative",children:(0,b.jsx)("input",{value:f,onChange:a=>g(a.target.value),placeholder:"Ask a question about your documents...",disabled:h||0===j.length,className:"w-full rounded-2xl border-2 border-gray-200 px-6 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-200 disabled:bg-gray-50 disabled:cursor-not-allowed text-lg shadow-lg"})}),(0,b.jsx)("button",{type:"submit",disabled:h||!f.trim()||0===j.length,className:"rounded-2xl bg-gradient-to-r from-purple-500 to-pink-600 px-8 py-4 text-white font-medium hover:from-purple-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-200",children:h?"Searching...":"Ask"})]})]})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__23f4ed35._.js.map