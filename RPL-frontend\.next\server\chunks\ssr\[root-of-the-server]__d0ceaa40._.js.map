{"version": 3, "sources": ["turbopack:///[project]/src/components/ChatPage.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/src/app/chat/page.tsx"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ChatPage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ChatPage.tsx\",\n    \"default\",\n);\n", "import ChatPage from '@/components/ChatPage';\r\n\r\nexport default function Chat() {\r\n  return <ChatPage />;\r\n}\r\n"], "names": [], "mappings": "sPAEe,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,6RAA+R,EAC5T,4DACA,gEAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,yQAA2Q,EACxS,wCACA,sJCLJ,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAQ,CAAA,CAAA,EAClB", "ignoreList": [0]}