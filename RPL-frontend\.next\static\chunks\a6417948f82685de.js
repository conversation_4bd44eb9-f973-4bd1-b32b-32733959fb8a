(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,a)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"warnOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},9623,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(43476),a=e.i(71645),s=e.i(22016),r=e.i(45678),l=e.i(13642);function n(){let[e,n]=(0,a.useState)([]),[i,d]=(0,a.useState)(""),[o,c]=(0,a.useState)(!1),x=(0,a.useRef)(null);(0,a.useEffect)(()=>{x.current&&(x.current.scrollTop=x.current.scrollHeight)},[e]);let m=async t=>{if(t.preventDefault(),!i.trim()||o)return;let a={id:Date.now().toString(),role:"user",content:i};n(e=>[...e,a]),d(""),c(!0);let s={id:(Date.now()+1).toString(),role:"assistant",content:""};n(e=>[...e,s]);try{var r;let t=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...e,a]})});if(!t.ok)throw Error("Failed to fetch response");let l=null==(r=t.body)?void 0:r.getReader(),i=new TextDecoder;if(l)for(;;){let{done:e,value:t}=await l.read();if(e)break;for(let e of i.decode(t).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)break;try{let e=JSON.parse(t);e.content&&n(t=>t.map(t=>t.id===s.id?{...t,content:t.content+e.content}:t))}catch(e){}}}}catch(e){console.error("Error:",e),n(e=>e.map(e=>e.id===s.id?{...e,content:"Sorry, I encountered an error. Please try again."}:e))}finally{c(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",style:{background:"linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"},children:[(0,t.jsx)(r.default,{}),(0,t.jsxs)("div",{className:"mx-auto w-full max-w-4xl py-8 px-4 mt-24 flex-grow",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]",children:"Pusat Informasi Publik"}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]",children:"Fakultas Teknik Universitas Indonesia"})]}),(0,t.jsx)("p",{className:"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]",children:"AI Assistant untuk Informasi dan Layanan FTUI"}),(0,t.jsxs)("div",{className:"flex justify-center gap-4 mt-6",children:[(0,t.jsx)("div",{className:"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]",children:"💬 Chat Mode"}),(0,t.jsx)(s.default,{href:"/rag",children:(0,t.jsx)("div",{className:"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]",children:"📚 RAG Mode"})})]})]}),(0,t.jsx)("div",{ref:x,className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[0===e.length?(0,t.jsxs)("div",{className:"text-center text-[#346ad5] mt-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"🏛️"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:"Selamat datang di PIP FTUI!"}),(0,t.jsx)("p",{className:"text-sm",children:"Silakan ajukan pertanyaan tentang Fakultas Teknik UI."})]}):e.map(e=>(0,t.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,t.jsxs)("div",{className:"\n                      max-w-[80%] rounded-2xl px-6 py-4 shadow-md\n                      ".concat("user"===e.role?"bg-[#346ad5] text-white":"bg-white text-gray-800 border border-gray-200","\n                    "),children:[(0,t.jsx)("div",{className:"text-xs mb-2 font-medium ".concat("user"===e.role?"text-blue-100":"text-[#346ad5]"),children:"user"===e.role?"You":"PIP Assistant"}),(0,t.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-wrap ".concat("user"===e.role?"text-white":"text-gray-700"),children:e.content})]})},e.id)),o&&(0,t.jsx)("div",{className:"flex justify-start",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-[#fae664] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,t.jsx)("span",{className:"text-sm text-[#346ad5]",children:"PIP Assistant sedang berpikir..."})]})})})]})}),(0,t.jsx)("form",{onSubmit:m,className:"relative flex-shrink-0",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4",children:[(0,t.jsx)("input",{value:i,onChange:e=>d(e.target.value),placeholder:"ASK PIP...",disabled:o,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,t.jsx)("button",{type:"submit",disabled:o||!i.trim(),className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]}),(0,t.jsx)(l.default,{})]})}}]);