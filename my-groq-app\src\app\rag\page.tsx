'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  sources?: string[];
}

interface Document {
  name: string;
}

export default function RAGChat() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const response = await fetch('/api/documents');
      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents.map((name: string) => ({ name })));
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/documents', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        await fetchDocuments();
        alert('Document uploaded successfully!');
        setShowUpload(false);
      } else {
        const error = await response.json();
        alert(`Upload failed: ${error.error}`);
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Failed to upload document');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/rag', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: input,
          top_k: 3,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch response');
      }

      const data = await response.json();

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.answer,
        sources: data.sources,
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running.',
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100">
      <div className="mx-auto w-full max-w-6xl py-8 px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            PIP FTUI - RAG Mode
          </h1>
          <p className="text-gray-600 mb-4">
            Chat with your documents using LlamaIndex & Groq
          </p>
          {/* Mode Switcher */}
          <div className="flex justify-center gap-4 mt-4">
            <Link href="/">
              <div className="bg-white text-gray-700 border-2 border-gray-300 px-6 py-2 rounded-lg font-medium hover:border-blue-500 hover:text-blue-600 transition-colors cursor-pointer">
                💬 Chat Mode
              </div>
            </Link>
            <div className="bg-purple-500 text-white px-6 py-2 rounded-lg font-medium">
              📚 RAG Mode
            </div>
          </div>
        </div>

        <div className="flex gap-6">
          {/* Sidebar - Documents */}
          <div className="w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-800">Documents</h2>
              <button
                onClick={() => setShowUpload(!showUpload)}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                {showUpload ? 'Cancel' : '+ Upload'}
              </button>
            </div>

            {showUpload && (
              <div className="mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300">
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleFileUpload}
                  disabled={isUploading}
                  accept=".pdf,.txt,.docx,.md,.html"
                  className="w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer"
                />
                {isUploading && (
                  <p className="text-sm text-purple-600 mt-2">Uploading...</p>
                )}
              </div>
            )}

            <div className="overflow-y-auto h-[calc(100%-100px)]">
              {documents.length === 0 ? (
                <div className="text-center text-gray-500 mt-8">
                  <div className="text-4xl mb-2">📄</div>
                  <p className="text-sm">No documents yet</p>
                  <p className="text-xs text-gray-400 mt-1">Upload a document to start</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {documents.map((doc, idx) => (
                    <div
                      key={idx}
                      className="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-2xl">📄</span>
                        <span className="text-sm text-gray-700 truncate flex-1">
                          {doc.name}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Main Chat Area */}
          <div className="flex-1">
            {/* Chat Messages */}
            <div className="bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200">
              <div className="p-6 space-y-6">
                {messages.length === 0 ? (
                  <div className="text-center text-gray-500 mt-8">
                    <div className="text-6xl mb-4">🤖</div>
                    <p className="text-lg">Ask questions about your documents!</p>
                    <p className="text-sm">Upload documents and start asking questions.</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`
                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md
                          ${message.role === 'user'
                            ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white'
                            : 'bg-gray-100 text-gray-800 border border-gray-200'}
                        `}
                      >
                        <div className={`text-xs mb-2 font-medium ${
                          message.role === 'user' ? 'text-purple-100' : 'text-gray-500'
                        }`}>
                          {message.role === 'user' ? 'You' : 'RAG Assistant'}
                        </div>
                        <div className={`text-sm leading-relaxed whitespace-pre-wrap ${
                          message.role === 'user' ? 'text-white' : 'text-gray-700'
                        }`}>
                          {message.content}
                        </div>
                        {message.sources && message.sources.length > 0 && (
                          <div className="mt-4 pt-4 border-t border-gray-300">
                            <p className="text-xs font-semibold text-gray-600 mb-2">Sources:</p>
                            <div className="space-y-2">
                              {message.sources.map((source, idx) => (
                                <div key={idx} className="text-xs text-gray-600 bg-white p-2 rounded border border-gray-200">
                                  {source}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 rounded-2xl px-6 py-4 shadow-md border border-gray-200">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-sm text-gray-500">Searching documents...</span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Input Form */}
            <form onSubmit={handleSubmit} className="flex gap-4">
              <div className="flex-1 relative">
                <input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Ask a question about your documents..."
                  disabled={isLoading || documents.length === 0}
                  className="w-full rounded-2xl border-2 border-gray-200 px-6 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-200 disabled:bg-gray-50 disabled:cursor-not-allowed text-lg shadow-lg"
                />
              </div>
              <button
                type="submit"
                disabled={isLoading || !input.trim() || documents.length === 0}
                className="rounded-2xl bg-gradient-to-r from-purple-500 to-pink-600 px-8 py-4 text-white font-medium hover:from-purple-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-200"
              >
                {isLoading ? 'Searching...' : 'Ask'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
