module.exports=[23566,a=>{a.v({className:"quicksand_b7e087bf-module__3kZyda__className",variable:"quicksand_b7e087bf-module__3kZyda__variable"})},27572,a=>{"use strict";a.s(["default",()=>f,"metadata",()=>e],27572);var b=a.i(7997),c=a.i(23566);let d={className:c.default.className,style:{fontFamily:"'Quicksand', 'Quicksand Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);let e={title:"RAG AI Chat with Llama 3.1 8B Instant",description:"Try to Chat"};function f({children:a}){return(0,b.jsx)("html",{lang:"en",children:(0,b.jsx)("body",{className:`${d.variable} antialiased`,children:a})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__1961a0d4._.js.map