{"version": 3, "sources": ["turbopack:///[next]/internal/font/google/quicksand_b7e087bf.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/quicksand_b7e087bf.js", "turbopack:///[project]/src/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"quicksand_b7e087bf-module__3kZyda__className\",\n  \"variable\": \"quicksand_b7e087bf-module__3kZyda__variable\",\n});\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Quicksand%22,%22arguments%22:[{%22variable%22:%22--font-quicksand%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22quicksand%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Quicksand', 'Quicksand Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Quicksand } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst quicksand = Quicksand({\n  variable: \"--font-quicksand\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"RAG AI Chat with Llama 3.1 8B Instant\",\n  description: \"Try to Chat\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${quicksand.variable} antialiased`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "0BAAA,EAAA,CAAA,CAAA,CACA,UAAA,+CACA,SAAA,6CACA,yFCHA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,oCACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECDnC,IAAM,EAAqB,CAChC,MAAO,wCACP,YAAa,aACf,EAEe,SAAS,EAAW,UACjC,CAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,cACT,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,UAAW,CAAA,EDTJ,ACSO,EAAU,QAAQ,CAAC,YAAY,CAAC,UAE7C,KAIT", "ignoreList": [0, 1]}