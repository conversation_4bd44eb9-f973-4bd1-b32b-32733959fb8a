(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,75144,e=>{"use strict";e.s(["ThemeProvider",()=>o,"useTheme",()=>u]);var t=e.i(43476),r=e.i(71645);let i=(0,r.createContext)(void 0);function o(e){let{children:o}=e,[u,n]=(0,r.useState)(!1);return(0,t.jsx)(i.Provider,{value:{isDarkMode:u,toggleDarkMode:()=>{n(e=>!e)}},children:o})}function u(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},30824,e=>{"use strict";e.s(["default",()=>i]);var t=e.i(43476),r=e.i(75144);function i(e){let{children:i}=e;return(0,t.jsx)(r.<PERSON>,{children:i})}}]);