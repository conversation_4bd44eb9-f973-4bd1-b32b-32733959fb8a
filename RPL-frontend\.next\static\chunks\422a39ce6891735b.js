(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,a,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},9623,e=>{"use strict";e.s(["default",()=>n]);var a=e.i(43476),t=e.i(71645),s=e.i(22016),l=e.i(45678),r=e.i(13642);function n(){let[e,n]=(0,t.useState)([]),[i,o]=(0,t.useState)(""),[d,c]=(0,t.useState)(!1),m=(0,t.useRef)(null);(0,t.useEffect)(()=>{m.current&&(m.current.scrollTop=m.current.scrollHeight)},[e]);let x=async a=>{if(a.preventDefault(),!i.trim()||d)return;let t={id:Date.now().toString(),role:"user",content:i};n(e=>[...e,t]),o(""),c(!0);let s={id:(Date.now()+1).toString(),role:"assistant",content:""};n(e=>[...e,s]);try{var l;let a=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...e,t]})});if(!a.ok)throw Error("Failed to fetch response");let r=null==(l=a.body)?void 0:l.getReader(),i=new TextDecoder;if(r)for(;;){let{done:e,value:a}=await r.read();if(e)break;for(let e of i.decode(a).split("\n"))if(e.startsWith("data: ")){let a=e.slice(6);if("[DONE]"===a)break;try{let e=JSON.parse(a);e.content&&n(a=>a.map(a=>a.id===s.id?{...a,content:a.content+e.content}:a))}catch(e){}}}}catch(e){console.error("Error:",e),n(e=>e.map(e=>e.id===s.id?{...e,content:"Sorry, I encountered an error. Please try again."}:e))}finally{c(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",style:{background:"linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"},children:[(0,a.jsx)(l.default,{}),(0,a.jsxs)("div",{className:"mx-auto w-full max-w-4xl py-8 px-4 mt-24 flex-grow",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]",children:"Pusat Informasi Publik"}),(0,a.jsx)("h2",{className:"text-2xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]",children:"Fakultas Teknik Universitas Indonesia"})]}),(0,a.jsx)("p",{className:"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]",children:"AI Assistant untuk Informasi dan Layanan FTUI"}),(0,a.jsxs)("div",{className:"flex justify-center gap-4 mt-6",children:[(0,a.jsx)("div",{className:"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]",children:"💬 Chat Mode"}),(0,a.jsx)(s.default,{href:"/rag",children:(0,a.jsx)("div",{className:"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]",children:"📚 RAG Mode"})})]})]}),(0,a.jsx)("div",{ref:m,className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[0===e.length?(0,a.jsxs)("div",{className:"text-center text-[#346ad5] mt-8",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"🏛️"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:"Selamat datang di PIP FTUI!"}),(0,a.jsx)("p",{className:"text-sm",children:"Silakan ajukan pertanyaan tentang Fakultas Teknik UI."})]}):e.map(e=>(0,a.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"\n                      max-w-[80%] rounded-2xl px-6 py-4 shadow-md\n                      ".concat("user"===e.role?"bg-[#346ad5] text-white":"bg-white text-gray-800 border border-gray-200","\n                    "),children:[(0,a.jsx)("div",{className:"text-xs mb-2 font-medium ".concat("user"===e.role?"text-blue-100":"text-[#346ad5]"),children:"user"===e.role?"You":"PIP Assistant"}),(0,a.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-wrap ".concat("user"===e.role?"text-white":"text-gray-700"),children:e.content})]})},e.id)),d&&(0,a.jsx)("div",{className:"flex justify-start",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-[#fae664] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,a.jsx)("span",{className:"text-sm text-[#346ad5]",children:"PIP Assistant sedang berpikir..."})]})})})]})}),(0,a.jsx)("form",{onSubmit:x,className:"relative flex-shrink-0",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4",children:[(0,a.jsx)("input",{value:i,onChange:e=>o(e.target.value),placeholder:"ASK PIP...",disabled:d,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,a.jsx)("button",{type:"submit",disabled:d||!i.trim(),className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]}),(0,a.jsx)(r.default,{})]})}},45254,e=>{"use strict";e.s(["default",()=>i]);var a=e.i(43476),t=e.i(71645),s=e.i(45678),l=e.i(13642),r=e.i(57688),n=e.i(75144);function i(){let{isDarkMode:e}=(0,n.useTheme)(),[i,o]=(0,t.useState)([]),[d,c]=(0,t.useState)(""),[m,x]=(0,t.useState)(!1);(0,t.useRef)(null);let f=(0,t.useRef)(null);(0,t.useEffect)(()=>{f.current&&(f.current.scrollTop=f.current.scrollHeight)},[i]);let u=async e=>{if(e.preventDefault(),!d.trim()||m)return;let a={id:Date.now().toString(),role:"user",content:d,timestamp:new Date().toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})};o(e=>[...e,a]),c(""),x(!0),setTimeout(()=>{let e={id:(Date.now()+1).toString(),role:"assistant",content:"This is a placeholder response from PIP FTUI assistant. The actual RAG functionality is in development.",timestamp:new Date().toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})};o(a=>[...a,e]),x(!1)},1e3)};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col relative overflow-x-hidden",children:[(0,a.jsxs)("div",{className:"fixed inset-0 w-full h-full -z-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 transition-colors duration-300 ".concat(e?"bg-transparent":"bg-white")}),(0,a.jsx)("div",{className:"absolute inset-0 transition-all duration-300 ".concat(e?"brightness-[0.4]":""),children:(0,a.jsx)(r.default,{src:"/Home_Page/Backround Design.svg",alt:"Background with yellow circles",fill:!0,className:"object-cover",priority:!0,quality:100})})]}),(0,a.jsx)(s.default,{}),(0,a.jsx)("div",{className:"flex flex-col pt-20 pb-4 px-8 min-h-screen",children:(0,a.jsxs)("div",{className:"w-full flex flex-col gap-4 mx-auto mb-4",style:{maxWidth:"calc(100% - 4rem)",minHeight:"calc(100vh - 9rem)"},children:[(0,a.jsx)("div",{className:"flex-1 backdrop-blur-sm rounded-[32px] shadow-[0_8px_32px_rgba(0,0,0,0.3)] p-8 overflow-hidden transition-colors duration-300 ".concat(e?"bg-gradient-to-br from-[#1e3a5f]/90 via-[#2d4a6e]/90 to-[#3d5a7e]/90":"bg-gradient-to-br from-[#5a6c7d]/90 via-[#5a7a9d]/90 to-[#4a6b8a]/90"),children:(0,a.jsxs)("div",{ref:f,className:"h-full overflow-y-auto space-y-4 pr-2 chat-scroll",children:[0===i.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-white/60 text-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"💬"}),(0,a.jsx)("p",{className:"text-xl font-[family-name:var(--font-comfortaa)]",children:"Ask PIP..."})]})}):i.map(e=>(0,a.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"max-w-[75%] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)] ".concat("user"===e.role?"bg-[#f5e6a3] text-gray-900":"bg-[#e8eef5] text-gray-900"),children:[(0,a.jsx)("div",{className:"text-[15px] leading-relaxed whitespace-pre-wrap font-[family-name:var(--font-quicksand)]",children:e.content}),e.timestamp&&(0,a.jsx)("div",{className:"text-[11px] text-gray-500 mt-2 text-right font-[family-name:var(--font-quicksand)]",children:e.timestamp}),e.attachments&&e.attachments.length>0&&(0,a.jsx)("div",{className:"mt-3 space-y-2",children:e.attachments.map((e,t)=>(0,a.jsxs)("div",{className:"bg-yellow-400 rounded-lg p-3 flex items-center gap-3",children:[(0,a.jsx)("div",{className:"text-3xl",children:"📄"}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("p",{className:"font-semibold text-sm",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:e.size})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{className:"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium",children:"View File"}),(0,a.jsx)("button",{className:"bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-xs font-medium",children:"Download File"})]})]},t))})]})},e.id)),m&&(0,a.jsx)("div",{className:"flex justify-start",children:(0,a.jsx)("div",{className:"bg-[#e8eef5] rounded-[24px] px-7 py-5 shadow-[0_4px_16px_rgba(0,0,0,0.15)]",children:(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})})})]})}),(0,a.jsx)("form",{onSubmit:u,className:"relative flex-shrink-0",children:(0,a.jsxs)("div",{className:"rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4 transition-colors duration-300 ".concat(e?"bg-gradient-to-r from-[#1e3a5f] via-[#2d4a6e] to-[#3d5a7e]":"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a]"),children:[(0,a.jsx)("input",{value:d,onChange:e=>c(e.target.value),placeholder:"ASK PIP...",disabled:m,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,a.jsx)("button",{type:"submit",disabled:m||!d.trim(),className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]})}),(0,a.jsx)(l.default,{})]})}},82557,e=>{"use strict";e.s(["default",()=>i]);var a=e.i(43476),t=e.i(57688),s=e.i(71645),l=e.i(45678),r=e.i(13642),n=e.i(75144);function i(){let{isDarkMode:e}=(0,n.useTheme)(),[i,o]=(0,s.useState)({nama:"",email:"",subject:"",pesan:""}),d=e=>{o({...i,[e.target.name]:e.target.value})};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col relative overflow-x-hidden",children:[(0,a.jsxs)("div",{className:"fixed inset-0 w-full h-full -z-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 transition-colors duration-300 ".concat(e?"bg-transparent":"bg-white")}),(0,a.jsx)("div",{className:"absolute inset-0 transition-all duration-300 ".concat(e?"brightness-[0.4]":""),children:(0,a.jsx)(t.default,{src:"/Home_Page/Backround Design.svg",alt:"Background with yellow circles",fill:!0,className:"object-cover",priority:!0,quality:100})})]}),(0,a.jsx)(l.default,{}),(0,a.jsx)("div",{className:"flex-grow pt-24 pb-8 px-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"rounded-3xl p-8 text-white shadow-2xl transition-colors duration-300 ".concat(e?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"),children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6 font-[family-name:var(--font-comfortaa)]",children:"Faculty Contact Info"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Head Office"}),(0,a.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung Dekanat FTUI Lt. 2",(0,a.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,a.jsx)("br",{}),"Kampus UI Depok",(0,a.jsx)("br",{}),"+6221 7863504, +6221 7863505"]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 font-[family-name:var(--font-comfortaa)]",children:"Kantor Humas dan Protokol"}),(0,a.jsxs)("p",{className:"text-sm leading-relaxed font-[family-name:var(--font-quicksand)]",children:["Gedung GK IPAJI lantai 1",(0,a.jsx)("br",{}),"Fakultas Teknik Universitas Indonesia",(0,a.jsx)("br",{}),"Kampus UI Depok",(0,a.jsx)("br",{}),"+6221 78888430 ext 106",(0,a.jsx)("br",{}),"<EMAIL>"]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("p",{className:"text-sm font-bold font-[family-name:var(--font-quicksand)]",children:"Mon – Fri 8:00A.M. – 4:00P.M."}),(0,a.jsx)("p",{className:"text-sm font-semibold font-[family-name:var(--font-quicksand)]",children:"Social Info"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("a",{href:"https://instagram.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/instagram 1.png",alt:"Instagram",fill:!0,className:"object-contain"})})}),(0,a.jsx)("a",{href:"https://linkedin.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/linkedin 1.png",alt:"LinkedIn",fill:!0,className:"object-contain"})})}),(0,a.jsx)("a",{href:"https://youtube.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/youtube 1.png",alt:"YouTube",fill:!0,className:"object-contain"})})}),(0,a.jsx)("a",{href:"https://facebook.com",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-80 transition-opacity",children:(0,a.jsx)("div",{className:"w-8 h-8 relative",children:(0,a.jsx)(t.default,{src:"/Footer/facebook 1.png",alt:"Facebook",fill:!0,className:"object-contain"})})})]})]}),(0,a.jsx)("div",{className:"rounded-3xl p-8 shadow-2xl transition-colors duration-300 ".concat(e?"bg-gradient-to-br from-[#2d4a6e] to-[#1e3a5f]":"bg-gradient-to-br from-[#7a8a9a] to-[#5a7a9d]"),children:(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",i),alert("Pesan Anda telah terkirim!"),o({nama:"",email:"",subject:"",pesan:""})},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Nama"}),(0,a.jsx)("input",{type:"text",name:"nama",value:i.nama,onChange:d,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan nama Anda"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Email"}),(0,a.jsx)("input",{type:"email",name:"email",value:i.email,onChange:d,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan email Anda"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Subject"}),(0,a.jsx)("input",{type:"text",name:"subject",value:i.subject,onChange:d,required:!0,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] font-[family-name:var(--font-quicksand)]",placeholder:"Masukkan subjek"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-semibold mb-2 font-[family-name:var(--font-quicksand)]",children:"Pesan"}),(0,a.jsx)("textarea",{name:"pesan",value:i.pesan,onChange:d,required:!0,rows:6,className:"w-full px-4 py-3 rounded-xl bg-white/80 backdrop-blur-sm text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#ffd954] resize-none font-[family-name:var(--font-quicksand)]",placeholder:"Tulis pesan Anda"})]}),(0,a.jsx)("button",{type:"submit",className:"w-full bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 font-bold py-3 rounded-xl transition-all shadow-lg font-[family-name:var(--font-comfortaa)]",children:"Kirim Pesan"})]})})]}),(0,a.jsxs)("div",{className:"rounded-3xl p-8 shadow-2xl transition-colors duration-300 ".concat(e?"bg-gradient-to-br from-[#1e3a5f] to-[#0f2744]":"bg-gradient-to-br from-[#4a6b8a] to-[#2d5a9e]"),children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-6 text-white font-[family-name:var(--font-comfortaa)]",children:"Departement Contact Info"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>",telp:"WA 7270042 - 78849042",whatsapp:"WA 081212776702 (sekretariat Teknik Mesin)"},{name:"Departemen Teknik Sipil",email:"<EMAIL>, <EMAIL>",telp:"+6221 7270029 - 7270028",whatsapp:"082211135202 (Sekretariat DTS)"},{name:"Departemen Teknik Elektro",email:"<EMAIL>",telp:"+6221 7270078 - 7863504",whatsapp:"081289606440"},{name:"Departemen Teknik Metalurgi",email:"<EMAIL>",telp:"+6221 78849044",whatsapp:"081519996009"},{name:"Departemen Teknik Kimia",email:"<EMAIL>",telp:"+6221 7863516",whatsapp:"082112025025"},{name:"Departemen Arsitektur",email:"<EMAIL>",telp:"+6221 7270062",whatsapp:"081296661126"},{name:"Departemen Teknik Industri",email:"<EMAIL>",telp:"+6221 7270041",whatsapp:"082112345678"},{name:"Departemen Teknik Komputer",email:"<EMAIL>",telp:"+6221 7863512",whatsapp:"081234567890"}].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl transition-shadow",children:[(0,a.jsx)("h3",{className:"text-[#2d5a9e] font-bold text-sm mb-3 font-[family-name:var(--font-comfortaa)]",children:e.name}),(0,a.jsxs)("div",{className:"space-y-2 text-xs font-[family-name:var(--font-quicksand)]",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-600",children:e.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:"Telp"}),(0,a.jsx)("p",{className:"text-gray-600",children:e.telp})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:"Whatsapp Only"}),(0,a.jsx)("p",{className:"text-gray-600",children:e.whatsapp})]})]})]},t))})]})]})}),(0,a.jsx)(r.default,{})]})}},4322,e=>{"use strict";e.s(["default",()=>n]);var a=e.i(43476),t=e.i(71645),s=e.i(22016),l=e.i(45678),r=e.i(13642);function n(){let[e,n]=(0,t.useState)([]),[i,o]=(0,t.useState)(""),[d,c]=(0,t.useState)(!1),[m,x]=(0,t.useState)([]),[f,u]=(0,t.useState)(!1),[h,p]=(0,t.useState)(!1),g=(0,t.useRef)(null),b=(0,t.useRef)(null);(0,t.useEffect)(()=>{g.current&&(g.current.scrollTop=g.current.scrollHeight)},[e]),(0,t.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=await fetch("/api/documents");if(e.ok){let a=await e.json();x(a.documents.map(e=>({name:e})))}}catch(e){console.error("Error fetching documents:",e)}},v=async e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(!t)return;u(!0);let s=new FormData;s.append("file",t);try{let e=await fetch("/api/documents",{method:"POST",body:s});if(e.ok)await j(),alert("Document uploaded successfully!"),p(!1);else{let a=await e.json();alert("Upload failed: ".concat(a.error))}}catch(e){console.error("Error uploading file:",e),alert("Failed to upload document")}finally{u(!1),b.current&&(b.current.value="")}},y=async e=>{if(e.preventDefault(),!i.trim()||d)return;let a={id:Date.now().toString(),role:"user",content:i};n(e=>[...e,a]),o(""),c(!0);try{let e=await fetch("/api/rag",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:i,top_k:3})});if(!e.ok)throw Error("Failed to fetch response");let a=await e.json(),t={id:(Date.now()+1).toString(),role:"assistant",content:a.answer,sources:a.sources};n(e=>[...e,t])}catch(a){console.error("Error:",a);let e={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running."};n(a=>[...a,e])}finally{c(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",style:{background:"linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"},children:[(0,a.jsx)(l.default,{}),(0,a.jsxs)("div",{className:"mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]",children:"PIP FTUI - RAG Mode"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]",children:"Retrieval-Augmented Generation"})]}),(0,a.jsx)("p",{className:"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]",children:"Cari informasi FTUI berdasarkan dokumen yang tersedia"}),(0,a.jsxs)("div",{className:"flex justify-center gap-4 mt-6",children:[(0,a.jsx)(s.default,{href:"/chat",children:(0,a.jsx)("div",{className:"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]",children:"💬 Chat Mode"})}),(0,a.jsx)("div",{className:"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]",children:"📚 RAG Mode"})]})]}),(0,a.jsxs)("div",{className:"flex gap-6",children:[(0,a.jsxs)("div",{className:"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Documents"}),(0,a.jsx)("button",{onClick:()=>p(!h),className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:h?"Cancel":"+ Upload"})]}),h&&(0,a.jsxs)("div",{className:"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300",children:[(0,a.jsx)("input",{ref:b,type:"file",onChange:v,disabled:f,accept:".pdf,.txt,.docx,.md,.html",className:"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer"}),f&&(0,a.jsx)("p",{className:"text-sm text-purple-600 mt-2",children:"Uploading..."})]}),(0,a.jsx)("div",{className:"overflow-y-auto h-[calc(100%-100px)]",children:0===m.length?(0,a.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"📄"}),(0,a.jsx)("p",{className:"text-sm",children:"No documents yet"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Upload a document to start"})]}):(0,a.jsx)("div",{className:"space-y-2",children:m.map((e,t)=>(0,a.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:"📄"}),(0,a.jsx)("span",{className:"text-sm text-gray-700 truncate flex-1",children:e.name})]})},t))})})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{ref:g,className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[0===e.length?(0,a.jsxs)("div",{className:"text-center text-[#346ad5] mt-8",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"📚"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:"Tanyakan tentang informasi FTUI"}),(0,a.jsx)("p",{className:"text-sm",children:"Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan."})]}):e.map(e=>(0,a.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"\n                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md\n                          ".concat("user"===e.role?"bg-[#346ad5] text-white":"bg-white text-gray-800 border border-gray-200","\n                        "),children:[(0,a.jsx)("div",{className:"text-xs mb-2 font-medium ".concat("user"===e.role?"text-blue-100":"text-[#346ad5]"),children:"user"===e.role?"You":"PIP RAG Assistant"}),(0,a.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-wrap ".concat("user"===e.role?"text-white":"text-gray-700"),children:e.content}),e.sources&&e.sources.length>0&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-300",children:[(0,a.jsx)("p",{className:"text-xs font-semibold text-gray-600 mb-2",children:"Sources:"}),(0,a.jsx)("div",{className:"space-y-2",children:e.sources.map((e,t)=>(0,a.jsx)("div",{className:"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200",children:e},t))})]})]})},e.id)),d&&(0,a.jsx)("div",{className:"flex justify-start",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-[#fae664] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,a.jsx)("span",{className:"text-sm text-[#346ad5]",children:"Mencari di dokumen..."})]})})})]})}),(0,a.jsx)("form",{onSubmit:y,className:"relative flex-shrink-0",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4",children:[(0,a.jsx)("input",{value:i,onChange:e=>o(e.target.value),placeholder:"ASK PIP...",disabled:d||0===m.length,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,a.jsx)("button",{type:"submit",disabled:d||!i.trim()||0===m.length,className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]})]})]}),(0,a.jsx)(r.default,{})]})}},49755,e=>{"use strict";e.s(["default",()=>l]);var a=e.i(43476),t=e.i(57688),s=e.i(22016);function l(){return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,a.jsx)(t.default,{src:"/Landing_Page/Background.png",alt:"FTUI Campus Background",fill:!0,className:"object-cover",priority:!0,quality:100}),(0,a.jsx)("div",{className:"absolute inset-0 bg-[#4a6b8a]/70"})]}),(0,a.jsxs)("div",{className:"relative z-10 text-center px-4 flex flex-col items-center",children:[(0,a.jsx)("div",{className:"mb-8 flex justify-center",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(t.default,{src:"/Landing_Page/PIP LOGO 2.svg",alt:"PIP FTUI Logo",width:700,height:250,className:"drop-shadow-2xl w-full max-w-[700px] h-auto",priority:!0})})}),(0,a.jsx)(s.default,{href:"/home",children:(0,a.jsx)("div",{className:"inline-block relative group cursor-pointer mt-4",children:(0,a.jsxs)("div",{className:"relative w-[180px] h-[65px]",children:[(0,a.jsx)(t.default,{src:"/Landing_Page/Button.svg",alt:"Dive In Button",width:180,height:65,className:"transition-transform duration-300 group-hover:scale-105 drop-shadow-lg"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-[#ffffff] text-[22px] font-bold font-[family-name:var(--font-comfortaa)] tracking-wide drop-shadow-sm",children:"Dive In"})})]})})})]})]})}}]);