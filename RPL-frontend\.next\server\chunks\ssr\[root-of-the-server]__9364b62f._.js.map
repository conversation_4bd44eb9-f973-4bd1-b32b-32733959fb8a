{"version": 3, "sources": ["turbopack:///[next]/internal/font/google/quicksand_b7e087bf.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/comfortaa_5e8afc88.module.css [app-rsc] (css module)", "turbopack:///[project]/src/components/Providers.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[next]/internal/font/google/quicksand_b7e087bf.js", "turbopack:///[next]/internal/font/google/comfortaa_5e8afc88.js", "turbopack:///[project]/src/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"quicksand_b7e087bf-module__3kZyda__className\",\n  \"variable\": \"quicksand_b7e087bf-module__3kZyda__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"comfortaa_5e8afc88-module__lBtixG__className\",\n  \"variable\": \"comfortaa_5e8afc88-module__lBtixG__variable\",\n});\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Providers.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Providers.tsx\",\n    \"default\",\n);\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Quicksand%22,%22arguments%22:[{%22variable%22:%22--font-quicksand%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22quicksand%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Quicksand', 'Quicksand Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Comfortaa%22,%22arguments%22:[{%22variable%22:%22--font-comfortaa%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22comfortaa%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Comfortaa', 'Comfortaa Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import type { Metada<PERSON> } from \"next\";\r\nimport { Quicksand, Comfortaa } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport Providers from \"@/components/Providers\";\r\n\r\nconst quicksand = Quicksand({\r\n  variable: \"--font-quicksand\",\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n});\r\n\r\nconst comfortaa = Comfortaa({\r\n  variable: \"--font-comfortaa\",\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n});\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"PIP FTUI - Pusat Informasi Publik\",\r\n  description: \"AI Assistant untuk Informasi dan Layanan Fakultas Teknik Universitas Indonesia\",\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <body\r\n        className={`${quicksand.variable} ${comfortaa.variable} antialiased`}\r\n      >\r\n        <Providers>\r\n          {children}\r\n        </Providers>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": "0BAAA,EAAA,CAAA,CAAA,CACA,UAAA,+CACA,SAAA,6CACA,aCHA,EAAA,CAAA,CAAA,CACA,UAAA,+CACA,SAAA,6CACA,wDCDe,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,8RAAgS,EAC7T,6DACA,+DAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,0QAA4Q,EACzS,yCACA,2JCLJ,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,oCACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECX1C,IAAA,EAAA,EAAA,CAAA,CAAA,MACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,oCACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAQ,AAAR,ECRlC,IAAA,EAAA,EAAA,CAAA,CAAA,OAcO,IAAM,EAAqB,CAChC,MAAO,oCACP,YAAa,gFACf,EAEe,SAAS,EAAW,CACjC,UAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,cACT,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,UAAW,CAAA,EFhBJ,AEgBO,EAAU,QAAQ,CAAC,CAAC,EDhB3B,ACgB6B,EAAU,QAAQ,CAAC,YAAY,CAAC,UAEpE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAS,CAAA,UACP,OAKX", "ignoreList": [0, 1, 2, 3, 4]}