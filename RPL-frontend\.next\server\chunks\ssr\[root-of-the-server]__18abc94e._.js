module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},62212,a=>{a.n(a.i(66114))},81241,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/ContactPage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ContactPage.tsx <module evaluation>","default")},78601,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/ContactPage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ContactPage.tsx","default")},83871,a=>{"use strict";a.i(81241);var b=a.i(78601);a.n(b)},11280,(a,b,c)=>{},18475,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(7997),c=a.i(83871);function d(){return(0,b.jsx)(c.default,{})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__18abc94e._.js.map