{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GAWEAN/RPL/RPL-frontend/src/app/api/documents/route.ts"], "sourcesContent": ["const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';\r\n\r\nexport async function POST(req: Request) {\r\n  try {\r\n    const formData = await req.formData();\r\n    const file = formData.get('file');\r\n\r\n    if (!file) {\r\n      return new Response(JSON.stringify({ error: 'No file provided' }), {\r\n        status: 400,\r\n        headers: { 'Content-Type': 'application/json' },\r\n      });\r\n    }\r\n\r\n    const uploadFormData = new FormData();\r\n    uploadFormData.append('file', file);\r\n\r\n    const response = await fetch(`${BACKEND_URL}/api/upload`, {\r\n      method: 'POST',\r\n      body: uploadFormData,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const error = await response.json();\r\n      return new Response(JSON.stringify({ error: error.detail || 'Failed to upload file' }), {\r\n        status: response.status,\r\n        headers: { 'Content-Type': 'application/json' },\r\n      });\r\n    }\r\n\r\n    const data = await response.json();\r\n    return new Response(JSON.stringify(data), {\r\n      headers: { 'Content-Type': 'application/json' },\r\n    });\r\n  } catch (error) {\r\n    console.error('Error uploading file:', error);\r\n    return new Response(\r\n      JSON.stringify({ error: 'Internal server error' }),\r\n      { status: 500, headers: { 'Content-Type': 'application/json' } }\r\n    );\r\n  }\r\n}\r\n\r\nexport async function GET() {\r\n  try {\r\n    const response = await fetch(`${BACKEND_URL}/api/documents`);\r\n\r\n    if (!response.ok) {\r\n      throw new Error('Failed to fetch documents');\r\n    }\r\n\r\n    const data = await response.json();\r\n    return new Response(JSON.stringify(data), {\r\n      headers: { 'Content-Type': 'application/json' },\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching documents:', error);\r\n    return new Response(\r\n      JSON.stringify({ error: 'Internal server error' }),\r\n      { status: 500, headers: { 'Content-Type': 'application/json' } }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,cAAc,6DAAuC;AAEpD,eAAe,KAAK,GAAY;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,QAAQ;QACnC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,OAAO;YAAmB,IAAI;gBACjE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,iBAAiB,IAAI;QAC3B,eAAe,MAAM,CAAC,QAAQ;QAE9B,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,WAAW,CAAC,EAAE;YACxD,QAAQ;YACR,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;gBAAE,OAAO,MAAM,MAAM,IAAI;YAAwB,IAAI;gBACtF,QAAQ,SAAS,MAAM;gBACvB,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,OAAO;YACxC,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAChD;YAAE,QAAQ;YAAK,SAAS;gBAAE,gBAAgB;YAAmB;QAAE;IAEnE;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,cAAc,CAAC;QAE3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,OAAO;YACxC,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAChD;YAAE,QAAQ;YAAK,SAAS;gBAAE,gBAAgB;YAAmB;QAAE;IAEnE;AACF", "debugId": null}}]}