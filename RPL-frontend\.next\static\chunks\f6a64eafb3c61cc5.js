(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,88143,(e,t,r)=>{"use strict";function n(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:i,objectFit:a}=e,l=n?40*n:t,s=o?40*o:r,u=l&&s?"viewBox='0 0 "+l+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},87690,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return o}});let n=["default","imgix","cloudinary","akamai","custom"],o={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8927,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImgProps",{enumerable:!0,get:function(){return s}}),e.r(33525);let n=e.r(88143),o=e.r(87690),i=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let u,c,f,{src:d,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:y,quality:b,width:v,height:x,fill:j=!1,style:_,overrideSrc:w,onLoad:P,onLoadingComplete:O,placeholder:S="empty",blurDataURL:E,fetchPriority:C,decoding:N="async",layout:R,objectFit:I,objectPosition:M,lazyBoundary:T,lazyRoot:k,...A}=e,{imgConf:L,showAltText:z,blurComplete:F,defaultLoader:U}=t,D=L||o.imageConfigDefault;if("allSizes"in D)u=D;else{let e=[...D.deviceSizes,...D.imageSizes].sort((e,t)=>e-t),t=D.deviceSizes.sort((e,t)=>e-t),n=null==(r=D.qualities)?void 0:r.sort((e,t)=>e-t);u={...D,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===U)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=A.loader||U;delete A.loader,delete A.srcSet;let G="__next_img_default"in B;if(G){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:r,...n}=t;return e(n)}}if(R){"fill"===R&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(_={..._,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!p&&(p=t)}let K="",q=l(v),W=l(x);if((s=d)&&"object"==typeof s&&(a(s)||void 0!==s.src)){let e=a(d)?d.default:d;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,f=e.blurHeight,E=E||e.blurDataURL,K=e.src,!j)if(q||W){if(q&&!W){let t=q/e.width;W=Math.round(e.height*t)}else if(!q&&W){let t=W/e.height;q=Math.round(e.width*t)}}else q=e.width,W=e.height}let V=!h&&("lazy"===g||void 0===g);(!(d="string"==typeof d?d:K)||d.startsWith("data:")||d.startsWith("blob:"))&&(m=!0,V=!1),u.unoptimized&&(m=!0),G&&!u.dangerouslyAllowSVG&&d.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=l(b),X=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:I,objectPosition:M}:{},z?{}:{color:"transparent"},_),J=F||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:q,heightInt:W,blurWidth:c,blurHeight:f,blurDataURL:E||"",objectFit:X.objectFit})+'")':'url("'+S+'")',Y=i.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Q=J?{backgroundSize:Y,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},$=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:i,sizes:a,loader:l}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:s,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,a),c=s.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:s.map((e,n)=>l({config:t,src:r,quality:i,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:l({config:t,src:r,quality:i,width:s[c]})}}({config:u,src:d,unoptimized:m,width:q,quality:H,sizes:p,loader:B});return{props:{...A,loading:V?"lazy":g,fetchPriority:C,width:q,height:W,decoding:N,className:y,style:{...X,...Q},sizes:$.sizes,srcSet:$.srcSet,src:w||$.src},meta:{unoptimized:m,priority:h,placeholder:S,fill:j}}}},98879,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return l}});let n=e.r(71645),o="undefined"==typeof window,i=o?()=>{}:n.useLayoutEffect,a=o?()=>{}:n.useEffect;function l(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}if(o){var s;null==t||null==(s=t.mountedInstances)||s.add(e.children),l()}return i(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),i(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),a(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},58908,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=e.r(55682)._(e.r(71645)).default.createContext({})},15986,(e,t,r)=>{"use strict";function n(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isInAmpMode",{enumerable:!0,get:function(){return n}})},25633,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return h},defaultHead:function(){return f}});let n=e.r(55682),o=e.r(90809),i=e.r(43476),a=o._(e.r(71645)),l=n._(e.r(98879)),s=e.r(58908),u=e.r(42732),c=e.r(15986);function f(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}e.r(33525);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let i=!0,a=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){a=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}return i}}()).reverse().map((e,t)=>{let r=e.key||t;return a.default.cloneElement(e,{key:r})})}let h=function(e){let{children:t}=e,r=(0,a.useContext)(s.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,i.jsx)(l.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18556,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let n=e.r(55682)._(e.r(71645)),o=e.r(87690),i=n.default.createContext(o.imageConfigDefault)},65856,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"RouterContext",{enumerable:!0,get:function(){return n}});let n=e.r(55682)._(e.r(71645)).default.createContext(null)},1948,(e,t,r)=>{"use strict";function n(e){var t;let{config:r,src:n,width:o,quality:i}=e,a=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o}}),n.__next_img_default=!0;let o=n},18581,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(71645);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},5500,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"Image",{enumerable:!0,get:function(){return x}});let n=e.r(55682),o=e.r(90809),i=e.r(43476),a=o._(e.r(71645)),l=n._(e.r(74080)),s=n._(e.r(25633)),u=e.r(8927),c=e.r(87690),f=e.r(18556);e.r(33525);let d=e.r(65856),p=n._(e.r(1948)),m=e.r(18581),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,n,o,i,a){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let b=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:l,width:s,decoding:u,className:c,style:f,fetchPriority:d,placeholder:p,loading:h,unoptimized:b,fill:v,onLoadRef:x,onLoadingCompleteRef:j,setBlurComplete:_,setShowAltText:w,sizesInput:P,onLoad:O,onError:S,...E}=e,C=(0,a.useCallback)(e=>{e&&(S&&(e.src=e.src),e.complete&&g(e,p,x,j,_,b,P))},[r,p,x,j,_,S,b,P]),N=(0,m.useMergedRef)(t,C);return(0,i.jsx)("img",{...E,...y(d),loading:h,width:s,height:l,decoding:u,"data-nimg":v?"fill":"1",className:c,style:f,sizes:o,srcSet:n,src:r,ref:N,onLoad:e=>{g(e.currentTarget,p,x,j,_,b,P)},onError:e=>{w(!0),"empty"!==p&&_(!0),S&&S(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...y(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,n),null):(0,i.jsx)(s.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let x=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(d.RouterContext),n=(0,a.useContext)(f.ImageConfigContext),o=(0,a.useMemo)(()=>{var e;let t=h||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:i}},[n]),{onLoad:l,onLoadingComplete:s}=e,m=(0,a.useRef)(l);(0,a.useEffect)(()=>{m.current=l},[l]);let g=(0,a.useRef)(s);(0,a.useEffect)(()=>{g.current=s},[s]);let[y,x]=(0,a.useState)(!1),[j,_]=(0,a.useState)(!1),{props:w,meta:P}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:y,showAltText:j});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b,{...w,unoptimized:P.unoptimized,placeholder:P.placeholder,fill:P.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:x,setShowAltText:_,sizesInput:e.sizes,ref:t}),P.priority?(0,i.jsx)(v,{isAppRouter:!r,imgAttributes:w}):null]})});("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},94909,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return s},getImageProps:function(){return l}});let n=e.r(55682),o=e.r(8927),i=e.r(5500),a=n._(e.r(1948));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=i.Image},57688,(e,t,r)=>{t.exports=e.r(94909)},98183,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},95057,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return i},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let n=e.r(90809)._(e.r(98183)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",a=e.pathname||"",l=e.hash||"",s=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),s&&"object"==typeof s&&(s=String(n.urlQueryToSearchParams(s)));let c=e.search||s&&"?"+s||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+i+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return i(e)}},18967,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return b},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return m},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return u},getLocationOrigin:function(){return l},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return f},stringifyError:function(){return x}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function f(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,m=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=e.r(18967),o=e.r(52817);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},84508,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},22016,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return g},useLinkStatus:function(){return b}});let n=e.r(90809),o=e.r(43476),i=n._(e.r(71645)),a=e.r(95057),l=e.r(8372),s=e.r(18581),u=e.r(18967),c=e.r(5550);e.r(33525);let f=e.r(91949),d=e.r(73668),p=e.r(99781);e.r(84508);let m=e.r(65165);function h(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){var t;let r,n,a,[g,b]=(0,i.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,i.useRef)(null),{href:x,as:j,children:_,prefetch:w=null,passHref:P,replace:O,shallow:S,scroll:E,onClick:C,onMouseEnter:N,onTouchStart:R,legacyBehavior:I=!1,onNavigate:M,ref:T,unstable_dynamicOnHover:k,...A}=e;r=_,I&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let L=i.default.useContext(l.AppRouterContext),z=!1!==w,F=!1!==w?null===(t=w)||"auto"===t?m.FetchStrategy.PPR:m.FetchStrategy.Full:m.FetchStrategy.PPR,{href:U,as:D}=i.default.useMemo(()=>{let e=h(x);return{href:e,as:j?h(j):e}},[x,j]);I&&(n=i.default.Children.only(r));let B=I?n&&"object"==typeof n&&n.ref:T,G=i.default.useCallback(e=>(null!==L&&(v.current=(0,f.mountLinkInstance)(e,U,L,F,z,b)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(e)}),[z,U,L,F,b]),K={ref:(0,s.useMergedRef)(G,B),onClick(e){I||"function"!=typeof C||C(e),I&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,o,a,l){let{nodeName:s}=e.currentTarget;if(!("A"===s.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}i.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==a||a,n.current)})}}(e,U,D,v,O,E,M))},onMouseEnter(e){I||"function"!=typeof N||N(e),I&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&z&&(0,f.onNavigationIntent)(e.currentTarget,!0===k)},onTouchStart:function(e){I||"function"!=typeof R||R(e),I&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&z&&(0,f.onNavigationIntent)(e.currentTarget,!0===k)}};return(0,u.isAbsoluteUrl)(D)?K.href=D:I&&!P&&("a"!==n.type||"href"in n.props)||(K.href=(0,c.addBasePath)(D)),a=I?i.default.cloneElement(n,K):(0,o.jsx)("a",{...A,...K,children:r}),(0,o.jsx)(y.Provider,{value:g,children:a})}let y=(0,i.createContext)(f.IDLE_LINK_STATUS),b=()=>(0,i.useContext)(y);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},45678,e=>{"use strict";e.s(["default",()=>i]);var t=e.i(43476),r=e.i(57688),n=e.i(22016),o=e.i(75144);function i(){let{isDarkMode:e,toggleDarkMode:i}=(0,o.useTheme)();return(0,t.jsx)("nav",{className:"fixed top-0 left-0 right-0 z-50 transition-colors duration-300 ".concat(e?"bg-[#1e3a5f]":"bg-[#2d5a9e]"),children:(0,t.jsx)("div",{className:"px-8 py-3 shadow-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)(n.default,{href:"/",className:"flex items-center hover:opacity-90 transition-opacity",children:(0,t.jsx)(r.default,{src:"/Landing_Page/PIP LOGO 2.svg",alt:"PIP FTUI Logo",width:160,height:55,className:"h-12 w-auto"})}),(0,t.jsxs)("div",{className:"flex items-center gap-8",children:[(0,t.jsx)("button",{onClick:i,className:"relative w-14 h-7 bg-white/20 rounded-full p-0.5 cursor-pointer transition-colors hover:bg-white/30","aria-label":"Toggle theme",children:(0,t.jsx)("div",{className:"absolute top-0.5 left-0.5 w-6 h-6 rounded-full bg-white shadow-md transform transition-transform duration-300 ease-in-out flex items-center justify-center ".concat(e?"translate-x-7":"translate-x-0"),children:e?(0,t.jsx)("span",{className:"text-sm",children:"☀️"}):(0,t.jsx)("span",{className:"text-sm",children:"🌙"})})}),(0,t.jsx)(n.default,{href:"/home",className:"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors",children:"Home"}),(0,t.jsx)(n.default,{href:"/documents",className:"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors",children:"Documents"}),(0,t.jsx)(n.default,{href:"/academics",className:"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors",children:"Academics"}),(0,t.jsx)(n.default,{href:"/contacts",className:"text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors",children:"Contacts"}),(0,t.jsx)(n.default,{href:"/prototypetesting",className:"text-yellow-300 font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-400 transition-colors border-2 border-yellow-300 px-4 py-1 rounded-lg",children:"🧪 Prototype"})]})]})})})}},13642,e=>{"use strict";e.s(["default",()=>i]);var t=e.i(43476),r=e.i(57688),n=e.i(22016),o=e.i(75144);function i(){let{isDarkMode:e}=(0,o.useTheme)();return(0,t.jsx)("footer",{className:"text-white mt-auto transition-colors duration-300 ".concat(e?"bg-[#1e3a5f]":"bg-[#2d5a9e]"),children:(0,t.jsx)("div",{className:"px-8 py-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-8 w-full",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,t.jsx)(r.default,{src:"/Landing_Page/PIP LOGO 2.svg",alt:"PIP FTUI Logo",width:220,height:80,className:"h-20 w-auto"}),(0,t.jsx)("p",{className:"text-white font-[family-name:var(--font-comfortaa)] text-base font-semibold",children:"@ PIP All Rights Reserved."})]}),(0,t.jsxs)("div",{className:"flex flex-col items-start md:items-end gap-4",children:[(0,t.jsxs)("div",{className:"flex items-start gap-2 text-white",children:[(0,t.jsx)(r.default,{src:"/Footer/marker-pin-02.png",alt:"Location Pin",width:20,height:20,className:"mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-sm font-[family-name:var(--font-comfortaa)] max-w-md text-left md:text-right leading-relaxed",children:"Pusgiwa UI, Gedung D Lt. 7, Jl. Prof. Dr. Fuad Hassan, Kukusan, Kecamatan Beji, Kota Depok, Jawa Barat 16425"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(n.default,{href:"https://instagram.com",target:"_blank",className:"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity","aria-label":"Instagram",children:(0,t.jsx)(r.default,{src:"/Footer/instagram 1.png",alt:"Instagram",width:36,height:36,className:"w-full h-full"})}),(0,t.jsx)(n.default,{href:"https://linkedin.com",target:"_blank",className:"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity","aria-label":"LinkedIn",children:(0,t.jsx)(r.default,{src:"/Footer/linkedin 1.png",alt:"LinkedIn",width:36,height:36,className:"w-full h-full"})}),(0,t.jsx)(n.default,{href:"https://youtube.com",target:"_blank",className:"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity","aria-label":"YouTube",children:(0,t.jsx)(r.default,{src:"/Footer/youtube 1.png",alt:"YouTube",width:36,height:36,className:"w-full h-full"})}),(0,t.jsx)(n.default,{href:"https://facebook.com",target:"_blank",className:"w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity","aria-label":"Facebook",children:(0,t.jsx)(r.default,{src:"/Footer/facebook 1.png",alt:"Facebook",width:36,height:36,className:"w-full h-full"})})]})]})]})})})}}]);