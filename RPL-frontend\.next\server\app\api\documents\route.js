var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/documents/route.js")
R.c("server/chunks/node_modules_4c981471._.js")
R.c("server/chunks/[root-of-the-server]__10d5398d._.js")
R.m("[project]/.next-internal/server/app/api/documents/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/documents/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/documents/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
