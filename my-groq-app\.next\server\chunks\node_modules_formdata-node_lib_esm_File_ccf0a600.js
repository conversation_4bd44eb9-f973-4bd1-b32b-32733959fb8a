module.exports=[56493,93633,34741,54769,e=>{"use strict";e.s(["File",()=>rl],56493),e.s(["Blob",()=>rn],34741);let t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function r(){}function o(e){return"object"==typeof e&&null!==e||"function"==typeof e}function n(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}let i=Promise,a=Promise.prototype.then,l=Promise.resolve.bind(i),s=Promise.reject.bind(i);function u(e){return new i(e)}function d(e,t,r){return a.call(e,t,r)}function c(e,t,o){d(d(e,t,o),void 0,r)}function f(e){d(e,void 0,r)}let h=e=>{if("function"==typeof queueMicrotask)h=queueMicrotask;else{let e=l(void 0);h=t=>d(e,t)}return h(e)};function b(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function p(e,t,r){try{var o;return o=b(e,t,r),l(o)}catch(e){return s(e)}}class _{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,o=r+1,n=e._elements,i=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(o=(r=r._next)._elements,t=0,0===o.length));)e(o[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}let y=t("[[AbortSteps]]"),m=t("[[ErrorSteps]]"),g=t("[[CancelSteps]]"),v=t("[[PullSteps]]"),w=t("[[ReleaseSteps]]");function S(e,t){var r,o;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?E(e):"closed"===t._state?(E(e),k(e)):(r=e,o=t._storedError,E(r),P(r,o))}function R(e,t){return tT(e._ownerReadableStream,t)}function T(e){var t,r;let o=e._ownerReadableStream;"readable"===o._state?P(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):(t=e,r=TypeError("Reader was released and can no longer be used to monitor the stream's closedness"),E(t),P(t,r)),o._readableStreamController[w](),o._reader=void 0,e._ownerReadableStream=void 0}function q(e){return TypeError("Cannot "+e+" a stream using a released reader")}function E(e){e._closedPromise=u((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function P(e,t){void 0!==e._closedPromise_reject&&(f(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function k(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let C=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},W=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function O(e,t){var r;if(void 0!==e&&"object"!=typeof(r=e)&&"function"!=typeof r)throw TypeError(`${t} is not an object.`)}function A(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function j(e,t){if(("object"!=typeof e||null===e)&&"function"!=typeof e)throw TypeError(`${t} is not an object.`)}function z(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function F(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function B(e){return Number(e)}function L(e,t){var r,o;let n=Number.MAX_SAFE_INTEGER,i=Number(e);if(!C(i=0===(r=i)?0:r))throw TypeError(`${t} is not a finite number`);if((i=0===(o=W(i))?0:o)<0||i>n)throw TypeError(`${t} is outside the accepted range of 0 to ${n}, inclusive`);return C(i)&&0!==i?i:0}function I(e){if(!o(e)||"function"!=typeof e.getReader)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function M(e){if(!o(e)||"function"!=typeof e.getWriter)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function $(e,t){if(!tS(e))throw TypeError(`${t} is not a ReadableStream.`)}function x(e,t){e._reader._readRequests.push(t)}function N(e,t,r){let o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function D(e){return e._reader._readRequests.length}function H(e){let t=e._reader;return void 0!==t&&!!Q(t)}class V{constructor(e){if(z(e,1,"ReadableStreamDefaultReader"),$(e,"First parameter"),tR(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");S(this,e),this._readRequests=new _}get closed(){return Q(this)?this._closedPromise:s(Y("closed"))}cancel(e){return Q(this)?void 0===this._ownerReadableStream?s(q("cancel")):R(this,e):s(Y("cancel"))}read(){let e,t;if(!Q(this))return s(Y("read"));if(void 0===this._ownerReadableStream)return s(q("read from"));let r=u((r,o)=>{e=r,t=o});return function(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[v](t)}(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!Q(this))throw Y("releaseLock");void 0!==this._ownerReadableStream&&(T(this),U(this,TypeError("Reader was released")))}}function Q(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof V}function U(e,t){let r=e._readRequests;e._readRequests=new _,r.forEach(e=>{e._errorSteps(t)})}function Y(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(V.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(V.prototype.cancel,"cancel"),n(V.prototype.read,"read"),n(V.prototype.releaseLock,"releaseLock"),"symbol"==typeof t.toStringTag&&Object.defineProperty(V.prototype,t.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});class G{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?d(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise?d(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let e=this._reader;return void 0===e?s(q("iterate")):d(e.read(),e=>{var t;return this._ongoingPromise=void 0,e.done&&(this._isFinished=!0,null==(t=this._reader)||t.releaseLock(),this._reader=void 0),e},e=>{var t;throw this._ongoingPromise=void 0,this._isFinished=!0,null==(t=this._reader)||t.releaseLock(),this._reader=void 0,e})}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(void 0===t)return s(q("finish iterating"));if(this._reader=void 0,!this._preventCancel){let r=t.cancel(e);return t.releaseLock(),d(r,()=>({value:e,done:!0}),void 0)}return t.releaseLock(),l({value:e,done:!0})}}let X={next(){return J(this)?this._asyncIteratorImpl.next():s(K("next"))},return(e){return J(this)?this._asyncIteratorImpl.return(e):s(K("return"))}};function J(e){if(!o(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof G}catch(e){return!1}}function K(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}"symbol"==typeof t.asyncIterator&&Object.defineProperty(X,t.asyncIterator,{value(){return this},writable:!0,configurable:!0});let Z=Number.isNaN||function(e){return e!=e};function ee(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}function et(e){return new Uint8Array(function(e,t,r){if(e.slice)return e.slice(t,r);let o=r-t,n=new ArrayBuffer(o);return ee(n,0,e,t,o),n}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength))}function er(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function eo(e,t,r){if("number"!=typeof r||Z(r)||r<0||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function en(e){e._queue=new _,e._queueTotalSize=0}class ei{constructor(){throw TypeError("Illegal constructor")}get view(){if(!es(this))throw eP("view");return this._view}respond(e){if(!es(this))throw eP("respond");if(z(e,1,"respond"),e=L(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");this._view.buffer,function(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=r.buffer,ew(e,t)}(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!es(this))throw eP("respondWithNewView");if(z(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");e.buffer,function(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let o=t.byteLength;r.buffer=t.buffer,ew(e,o)}(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ei.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),n(ei.prototype.respond,"respond"),n(ei.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof t.toStringTag&&Object.defineProperty(ei.prototype,t.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ea{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!el(this))throw ek("byobRequest");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ei.prototype);o._associatedReadableByteStreamController=e,o._view=r,e._byobRequest=o}return e._byobRequest}(this)}get desiredSize(){if(!el(this))throw ek("desiredSize");return eE(this)}close(){if(!el(this))throw ek("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);!function(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0)return e._closeRequested=!0;if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw eT(e,t),t}eR(e),tq(t)}}(this)}enqueue(e){if(!el(this))throw ek("enqueue");if(z(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);!function(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let o=t.buffer,n=t.byteOffset,i=t.byteLength;if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();t.buffer,eg(e),t.buffer=t.buffer,"none"===t.readerType&&ep(e,t)}H(r)?(function(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;eq(e,t._readRequests.shift())}}(e),0===D(r))?eh(e,o,n,i):(e._pendingPullIntos.length>0&&eS(e),N(r,new Uint8Array(o,n,i),!1)):eO(r)?(eh(e,o,n,i),ev(e)):eh(e,o,n,i),eu(e)}(this,e)}error(e){if(!el(this))throw ek("error");eT(this,e)}[g](e){ed(this),en(this);let t=this._cancelAlgorithm(e);return eR(this),t}[v](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void eq(this,e);let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}let o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}x(t,e),eu(this)}[w](){if(this._pendingPullIntos.length>0){let e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new _,this._pendingPullIntos.push(e)}}}function el(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ea}function es(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ei}function eu(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!(H(t)&&D(t)>0||eO(t)&&eW(t)>0||eE(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,c(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,eu(e)),null),t=>(eT(e,t),null))}}function ed(e){eg(e),e._pendingPullIntos=new _}function ec(e,t){let r=!1;"closed"===e._state&&(r=!0);let o=ef(t);"default"===t.readerType?N(e,o,r):function(e,t,r){let o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function ef(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function eh(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function eb(e,t,r,o){let n;try{n=t.slice(r,r+o)}catch(t){throw eT(e,t),t}eh(e,n,0,o)}function ep(e,t){t.bytesFilled>0&&eb(e,t.buffer,t.byteOffset,t.bytesFilled),eS(e)}function e_(e,t){let r=t.elementSize,o=t.bytesFilled-t.bytesFilled%r,n=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),i=t.bytesFilled+n,a=i-i%r,l=n,s=!1;a>o&&(l=a-t.bytesFilled,s=!0);let u=e._queue;for(;l>0;){let r=u.peek(),o=Math.min(l,r.byteLength),n=t.byteOffset+t.bytesFilled;ee(t.buffer,n,r.buffer,r.byteOffset,o),r.byteLength===o?u.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,ey(e,o,t),l-=o}return s}function ey(e,t,r){r.bytesFilled+=t}function em(e){0===e._queueTotalSize&&e._closeRequested?(eR(e),tq(e._controlledReadableByteStream)):eu(e)}function eg(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function ev(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;let t=e._pendingPullIntos.peek();e_(e,t)&&(eS(e),ec(e._controlledReadableByteStream,t))}}function ew(e,t){let r=e._pendingPullIntos.peek();eg(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&eS(e);let r=e._controlledReadableByteStream;if(eO(r))for(;eW(r)>0;)ec(r,eS(e))}(e,r):function(e,t,r){if(ey(0,t,r),"none"===r.readerType)return ep(e,r),ev(e);if(r.bytesFilled<r.elementSize)return;eS(e);let o=r.bytesFilled%r.elementSize;if(o>0){let t=r.byteOffset+r.bytesFilled;eb(e,r.buffer,t-o,o)}r.bytesFilled-=o,ec(e._controlledReadableByteStream,r),ev(e)}(e,t,r),eu(e)}function eS(e){return e._pendingPullIntos.shift()}function eR(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function eT(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(ed(e),en(e),eR(e),tE(r,t))}function eq(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,em(e);let o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function eE(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function eP(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function ek(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function eC(e,t){e._reader._readIntoRequests.push(t)}function eW(e){return e._reader._readIntoRequests.length}function eO(e){let t=e._reader;return void 0!==t&&!!ej(t)}Object.defineProperties(ea.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ea.prototype.close,"close"),n(ea.prototype.enqueue,"enqueue"),n(ea.prototype.error,"error"),"symbol"==typeof t.toStringTag&&Object.defineProperty(ea.prototype,t.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class eA{constructor(e){if(z(e,1,"ReadableStreamBYOBReader"),$(e,"First parameter"),tR(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!el(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");S(this,e),this._readIntoRequests=new _}get closed(){return ej(this)?this._closedPromise:s(eF("closed"))}cancel(e){return ej(this)?void 0===this._ownerReadableStream?s(q("cancel")):R(this,e):s(eF("cancel"))}read(e){let t,r;if(!ej(this))return s(eF("read"));if(!ArrayBuffer.isView(e))return s(TypeError("view must be an array buffer view"));if(0===e.byteLength)return s(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return s(TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return s(q("read from"));let o=u((e,o)=>{t=e,r=o});return function(e,t,r){let o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?r._errorSteps(o._storedError):function(e,t,r){let o=e._controlledReadableByteStream,n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);let i=t.constructor,a=t.buffer,l={buffer:a,bufferByteLength:a.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),eC(o,r);if("closed"!==o._state){if(e._queueTotalSize>0){if(e_(e,l)){let t=ef(l);return em(e),r._chunkSteps(t)}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");return eT(e,t),r._errorSteps(t)}}e._pendingPullIntos.push(l),eC(o,r),eu(e)}else{let e=new i(l.buffer,l.byteOffset,0);r._closeSteps(e)}}(o._readableStreamController,t,r)}(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),o}releaseLock(){if(!ej(this))throw eF("releaseLock");void 0!==this._ownerReadableStream&&(T(this),ez(this,TypeError("Reader was released")))}}function ej(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof eA}function ez(e,t){let r=e._readIntoRequests;e._readIntoRequests=new _,r.forEach(e=>{e._errorSteps(t)})}function eF(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function eB(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(Z(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function eL(e){let{size:t}=e;return t||(()=>1)}function eI(e,t){var r;O(e,t);let o=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===o?void 0:B(o),size:void 0===n?void 0:(A(r=n,`${t} has member 'size' that`),e=>B(r(e)))}}Object.defineProperties(eA.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(eA.prototype.cancel,"cancel"),n(eA.prototype.read,"read"),n(eA.prototype.releaseLock,"releaseLock"),"symbol"==typeof t.toStringTag&&Object.defineProperty(eA.prototype,t.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let eM="function"==typeof AbortController;class e${constructor(e={},t={}){void 0===e?e=null:j(e,"First parameter");let r=eI(t,"Second parameter"),o=function(e,t){O(e,t);let r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,i=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===r?void 0:(A(r,`${t} has member 'abort' that`),t=>p(r,e,[t])),close:void 0===o?void 0:(A(o,`${t} has member 'close' that`),()=>p(o,e,[])),start:void 0===n?void 0:(A(n,`${t} has member 'start' that`),t=>b(n,e,[t])),write:void 0===a?void 0:(A(a,`${t} has member 'write' that`),(t,r)=>p(a,e,[t,r])),type:i}}(e,"First parameter");if(this._state="writable",this._storedError=void 0,this._writer=void 0,this._writableStreamController=void 0,this._writeRequests=new _,this._inFlightWriteRequest=void 0,this._closeRequest=void 0,this._inFlightCloseRequest=void 0,this._pendingAbortRequest=void 0,this._backpressure=!1,void 0!==o.type)throw RangeError("Invalid type is specified");let n=eL(r);!function(e,t,r,o){let n,i,a,s,u=Object.create(e1.prototype);n=void 0!==t.start?()=>t.start(u):()=>{},i=void 0!==t.write?e=>t.write(e,u):()=>l(void 0),a=void 0!==t.close?()=>t.close():()=>l(void 0),s=void 0!==t.abort?e=>t.abort(e):()=>l(void 0),u._controlledWritableStream=e,e._writableStreamController=u,u._queue=void 0,u._queueTotalSize=void 0,en(u),u._abortReason=void 0,u._abortController=function(){if(eM)return new AbortController}(),u._started=!1,u._strategySizeAlgorithm=o,u._strategyHWM=r,u._writeAlgorithm=i,u._closeAlgorithm=a,u._abortAlgorithm=s,eX(e,0>=e6(u)),c(l(n()),()=>(u._started=!0,e4(u),null),t=>(u._started=!0,eV(e,t),null))}(this,o,eB(r,1),n)}get locked(){if(!ex(this))throw e7("locked");return eN(this)}abort(e){return ex(this)?eN(this)?s(TypeError("Cannot abort a stream that already has a writer")):eD(this,e):s(e7("abort"))}close(){return ex(this)?eN(this)?s(TypeError("Cannot close a stream that already has a writer")):eY(this)?s(TypeError("Cannot close an already-closing stream")):eH(this):s(e7("close"))}getWriter(){if(!ex(this))throw e7("getWriter");return new eJ(this)}}function ex(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof e$}function eN(e){return void 0!==e._writer}function eD(e,t){var r;if("closed"===e._state||"errored"===e._state)return l(void 0);e._writableStreamController._abortReason=t,null==(r=e._writableStreamController._abortController)||r.abort(t);let o=e._state;if("closed"===o||"errored"===o)return l(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);let i=u((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}});return e._pendingAbortRequest._promise=i,n||eQ(e,t),i}function eH(e){var t;let r=e._state;if("closed"===r||"errored"===r)return s(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let o=u((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===r&&ts(n),eo(t=e._writableStreamController,e0,0),e4(t),o}function eV(e,t){"writable"!==e._state?eU(e):eQ(e,t)}function eQ(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let o=e._writer;void 0!==o&&eZ(o,t),void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest&&r._started&&eU(e)}function eU(e){e._state="errored",e._writableStreamController[m]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new _,void 0===e._pendingAbortRequest)return void eG(e);let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void eG(e);c(e._writableStreamController[y](r._reason),()=>(r._resolve(),eG(e),null),t=>(r._reject(t),eG(e),null))}function eY(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function eG(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&to(t,e._storedError)}function eX(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?ti(r):ts(r)),e._backpressure=t}Object.defineProperties(e$.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),n(e$.prototype.abort,"abort"),n(e$.prototype.close,"close"),n(e$.prototype.getWriter,"getWriter"),"symbol"==typeof t.toStringTag&&Object.defineProperty(e$.prototype,t.toStringTag,{value:"WritableStream",configurable:!0});class eJ{constructor(e){if(z(e,1,"WritableStreamDefaultWriter"),function(e,t){if(!ex(e))throw TypeError(`${t} is not a WritableStream.`)}(e,"First parameter"),eN(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let t=e._state;if("writable"===t)!eY(e)&&e._backpressure?ti(this):function(e){ti(e),ts(e)}(this),tr(this);else if("erroring"===t)ta(this,e._storedError),tr(this);else if("closed"===t)(function(e){ti(e),ts(e)})(this),tr(this),tn(this);else{let t=e._storedError;ta(this,t),function(e,t){tr(e),to(e,t)}(this,t)}}get closed(){return eK(this)?this._closedPromise:s(te("closed"))}get desiredSize(){if(!eK(this))throw te("desiredSize");if(void 0===this._ownerWritableStream)throw tt("desiredSize");let e=this._ownerWritableStream,t=e._state;return"errored"===t||"erroring"===t?null:"closed"===t?0:e6(e._writableStreamController)}get ready(){return eK(this)?this._readyPromise:s(te("ready"))}abort(e){return eK(this)?void 0===this._ownerWritableStream?s(tt("abort")):eD(this._ownerWritableStream,e):s(te("abort"))}close(){if(!eK(this))return s(te("close"));let e=this._ownerWritableStream;return void 0===e?s(tt("close")):eY(e)?s(TypeError("Cannot close an already-closing stream")):eH(this._ownerWritableStream)}releaseLock(){if(!eK(this))throw te("releaseLock");void 0!==this._ownerWritableStream&&function(e){var t,r;let o=e._ownerWritableStream,n=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");eZ(e,n),"pending"===e._closedPromiseState?to(e,n):(t=e,r=n,tr(t),to(t,r)),o._writer=void 0,e._ownerWritableStream=void 0}(this)}write(e){return eK(this)?void 0===this._ownerWritableStream?s(tt("write to")):function(e,t){let r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return e5(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return s(tt("write to"));let i=r._state;if("errored"===i)return s(r._storedError);if(eY(r)||"closed"===i)return s(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return s(r._storedError);let a=u((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{eo(e,t,r)}catch(t){return void e5(e,t)}let o=e._controlledWritableStream;eY(o)||"writable"!==o._state||eX(o,0>=e6(e)),e4(e)}(o,t,n),a}(this,e):s(te("write"))}}function eK(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof eJ}function eZ(e,t){"pending"===e._readyPromiseState?tl(e,t):ta(e,t)}Object.defineProperties(eJ.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),n(eJ.prototype.abort,"abort"),n(eJ.prototype.close,"close"),n(eJ.prototype.releaseLock,"releaseLock"),n(eJ.prototype.write,"write"),"symbol"==typeof t.toStringTag&&Object.defineProperty(eJ.prototype,t.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let e0={};class e1{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!e3(this))throw e2("abortReason");return this._abortReason}get signal(){if(!e3(this))throw e2("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!e3(this))throw e2("error");"writable"===this._controlledWritableStream._state&&e9(this,e)}[y](e){let t=this._abortAlgorithm(e);return e8(this),t}[m](){en(this)}}function e3(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof e1}function e8(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function e6(e){return e._strategyHWM-e._queueTotalSize}function e4(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void eU(t);if(0===e._queue.length)return;let r=e._queue.peek().value;r===e0?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,er(e);let r=e._closeAlgorithm();e8(e),c(r,()=>((function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&tn(t)})(t),null),e=>(t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),eV(t,e),null))}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),c(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;return er(e),eY(r)||"writable"!==t||eX(r,0>=e6(e)),e4(e),null},t=>("writable"===r._state&&e8(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,eV(r,t),null))}(e,r)}function e5(e,t){"writable"===e._controlledWritableStream._state&&e9(e,t)}function e9(e,t){let r=e._controlledWritableStream;e8(e),eQ(r,t)}function e7(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function e2(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function te(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function tt(e){return TypeError("Cannot "+e+" a stream using a released writer")}function tr(e){e._closedPromise=u((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function to(e,t){void 0!==e._closedPromise_reject&&(f(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function tn(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function ti(e){e._readyPromise=u((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function ta(e,t){ti(e),tl(e,t)}function tl(e,t){void 0!==e._readyPromise_reject&&(f(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function ts(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(e1.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(e1.prototype,t.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});let tu="undefined"!=typeof DOMException?DOMException:void 0,td=!function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(tu)?function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}():tu;function tc(e,t,r,o,n,i){let a=e.getReader(),b=t.getWriter();tS(e)&&(e._disturbed=!0);let p,_,y,m=!1,g=!1,v="readable",w="writable",S=!1,R=!1,T=u(e=>{y=e}),q=Promise.resolve(void 0);return u((E,P)=>{let k;function C(){if(m)return;let e=u((e,t)=>{!function r(o){o?e():d(m?l(!0):d(b.ready,()=>d(a.read(),e=>!!e.done||(f(q=b.write(e.value)),!1))),r,t)}(!1)});f(e)}function W(){return v="closed",r?z():j(()=>(ex(t)&&(S=eY(t),w=t._state),S||"closed"===w?l(void 0):"erroring"===w||"errored"===w?s(_):(S=!0,b.close())),!1,void 0),null}function O(e){return m||(v="errored",p=e,o?z(!0,e):j(()=>b.abort(e),!0,e)),null}function A(e){return g||(w="errored",_=e,n?z(!0,e):j(()=>a.cancel(e),!0,e)),null}if(void 0!==i&&(k=()=>{let e=void 0!==i.reason?i.reason:new td("Aborted","AbortError"),t=[];o||t.push(()=>"writable"===w?b.abort(e):l(void 0)),n||t.push(()=>"readable"===v?a.cancel(e):l(void 0)),j(()=>Promise.all(t.map(e=>e())),!0,e)},i.aborted?k():i.addEventListener("abort",k)),tS(e)&&(v=e._state,p=e._storedError),ex(t)&&(w=t._state,_=t._storedError,S=eY(t)),tS(e)&&ex(t)&&(R=!0,y()),"errored"===v)O(p);else if("erroring"===w||"errored"===w)A(_);else if("closed"===v)W();else if(S||"closed"===w){let e=TypeError("the destination writable stream closed before all data could be piped to it");n?z(!0,e):j(()=>a.cancel(e),!0,e)}function j(e,t,r){function o(){let e;return"writable"!==w||S?n():c(l(function t(){if(e!==q)return e=q,d(q,t,t)}()),n),null}function n(){return e?c(e(),()=>F(t,r),e=>F(!0,e)):F(t,r),null}m||(m=!0,R?o():c(T,o))}function z(e,t){j(void 0,e,t)}function F(e,t){return g=!0,b.releaseLock(),a.releaseLock(),void 0!==i&&i.removeEventListener("abort",k),e?P(t):E(void 0),null}m||(c(a.closed,W,O),c(b.closed,function(){return g||(w="closed"),null},A)),R?C():h(()=>{R=!0,y(),C()})})}class tf{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!th(this))throw tg("desiredSize");return ty(this)}close(){if(!th(this))throw tg("close");if(!tm(this))throw TypeError("The stream is not in a state that permits close");!function(e){if(!tm(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(tp(e),tq(t))}(this)}enqueue(e){if(!th(this))throw tg("enqueue");if(!tm(this))throw TypeError("The stream is not in a state that permits enqueue");if(!tm(this))return;let t=this._controlledReadableStream;if(tR(t)&&D(t)>0)N(t,e,!1);else{let t;try{t=this._strategySizeAlgorithm(e)}catch(e){throw t_(this,e),e}try{eo(this,e,t)}catch(e){throw t_(this,e),e}}tb(this)}error(e){if(!th(this))throw tg("error");t_(this,e)}[g](e){en(this);let t=this._cancelAlgorithm(e);return tp(this),t}[v](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=er(this);this._closeRequested&&0===this._queue.length?(tp(this),tq(t)):tb(this),e._chunkSteps(r)}else x(t,e),tb(this)}[w](){}}function th(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof tf}function tb(e){if(function(e){let t=e._controlledReadableStream;return!!tm(e)&&!!e._started&&!!(tR(t)&&D(t)>0||ty(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,c(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tb(e)),null),t=>(t_(e,t),null))}}function tp(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function t_(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(en(e),tp(e),tE(r,t))}function ty(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tm(e){return!e._closeRequested&&"readable"===e._controlledReadableStream._state}function tg(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tv(e,t){O(e,t);let r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!o,preventClose:!!n,signal:i}}Object.defineProperties(tf.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),n(tf.prototype.close,"close"),n(tf.prototype.enqueue,"enqueue"),n(tf.prototype.error,"error"),"symbol"==typeof t.toStringTag&&Object.defineProperty(tf.prototype,t.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class tw{constructor(e={},t={}){void 0===e?e=null:j(e,"First parameter");let r=eI(t,"Second parameter"),o=function(e,t){O(e,t);let r=null==e?void 0:e.autoAllocateChunkSize,o=null==e?void 0:e.cancel,n=null==e?void 0:e.pull,i=null==e?void 0:e.start,a=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===r?void 0:L(r,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===o?void 0:(A(o,`${t} has member 'cancel' that`),t=>p(o,e,[t])),pull:void 0===n?void 0:(A(n,`${t} has member 'pull' that`),t=>p(n,e,[t])),start:void 0===i?void 0:(A(i,`${t} has member 'start' that`),t=>b(i,e,[t])),type:void 0===a?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(a,`${t} has member 'type' that`)}}(e,"First parameter");if(this._state="readable",this._reader=void 0,this._storedError=void 0,this._disturbed=!1,"bytes"===o.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let o,n,i,a=Object.create(ea.prototype);o=void 0!==t.start?()=>t.start(a):()=>{},n=void 0!==t.pull?()=>t.pull(a):()=>l(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>l(void 0);let s=t.autoAllocateChunkSize;if(0===s)throw TypeError("autoAllocateChunkSize must be greater than 0");a._controlledReadableByteStream=e,a._pullAgain=!1,a._pulling=!1,a._byobRequest=null,a._queue=a._queueTotalSize=void 0,en(a),a._closeRequested=!1,a._started=!1,a._strategyHWM=r,a._pullAlgorithm=n,a._cancelAlgorithm=i,a._autoAllocateChunkSize=s,a._pendingPullIntos=new _,e._readableStreamController=a,c(l(o()),()=>(a._started=!0,eu(a),null),e=>(eT(a,e),null))}(this,o,eB(r,0))}else{let e=eL(r);!function(e,t,r,o){let n,i,a,s=Object.create(tf.prototype);n=void 0!==t.start?()=>t.start(s):()=>{},i=void 0!==t.pull?()=>t.pull(s):()=>l(void 0),a=void 0!==t.cancel?e=>t.cancel(e):()=>l(void 0),s._controlledReadableStream=e,s._queue=void 0,s._queueTotalSize=void 0,en(s),s._started=!1,s._closeRequested=!1,s._pullAgain=!1,s._pulling=!1,s._strategySizeAlgorithm=o,s._strategyHWM=r,s._pullAlgorithm=i,s._cancelAlgorithm=a,e._readableStreamController=s,c(l(n()),()=>(s._started=!0,tb(s),null),e=>(t_(s,e),null))}(this,o,eB(r,1),e)}}get locked(){if(!tS(this))throw tP("locked");return tR(this)}cancel(e){return tS(this)?tR(this)?s(TypeError("Cannot cancel a stream that already has a reader")):tT(this,e):s(tP("cancel"))}getReader(e){if(!tS(this))throw tP("getReader");return void 0===function(e,t){O(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?new V(this):new eA(this)}pipeThrough(e,t={}){if(!I(this))throw tP("pipeThrough");z(e,1,"pipeThrough");let r=function(e,t){O(e,t);let r=null==e?void 0:e.readable;F(r,"readable","ReadableWritablePair"),function(e,t){if(!I(e))throw TypeError(`${t} is not a ReadableStream.`)}(r,`${t} has member 'readable' that`);let o=null==e?void 0:e.writable;return F(o,"writable","ReadableWritablePair"),function(e,t){if(!M(e))throw TypeError(`${t} is not a WritableStream.`)}(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=tv(t,"Second parameter");if(this.locked)throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(r.writable.locked)throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return f(tc(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){let r;if(!I(this))return s(tP("pipeTo"));if(void 0===e)return s("Parameter 1 is required in 'pipeTo'.");if(!M(e))return s(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=tv(t,"Second parameter")}catch(e){return s(e)}return this.locked?s(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):e.locked?s(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):tc(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!I(this))throw tP("tee");if(this.locked)throw TypeError("Cannot tee a stream that already has a reader");return!function(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}(this)?function(e,t){let r=e.getReader(),o,n,i,a,s,d=!1,f=!1,h=!1,b=!1,p=u(e=>{s=e});function _(){return d?f=!0:(d=!0,c(r.read(),e=>{if(f=!1,e.done)return h||i.close(),b||a.close(),h&&b||s(void 0),null;let t=e.value;return h||i.enqueue(t),b||a.enqueue(t),d=!1,f&&_(),null},()=>(d=!1,null))),l(void 0)}let y=new tw({start(e){i=e},pull:_,cancel:function(e){if(h=!0,o=e,b){let e=[o,n],t=r.cancel(e);s(t)}return p}}),m=new tw({start(e){a=e},pull:_,cancel:function(e){if(b=!0,n=e,h){let e=[o,n],t=r.cancel(e);s(t)}return p}});return c(r.closed,void 0,e=>(i.error(e),a.error(e),h&&b||s(void 0),null)),[y,m]}(this):function(e){let t,r,o,n,i,a=e.getReader(),s=!1,d=!1,f=!1,h=!1,b=!1,p=!1,_=u(e=>{i=e});function y(e){c(e.closed,void 0,t=>(e!==a||(o.error(t),n.error(t),b&&p||i(void 0)),null))}function m(){s&&(a.releaseLock(),y(a=e.getReader()),s=!1),c(a.read(),e=>{var t,r;if(f=!1,h=!1,e.done)return b||o.close(),p||n.close(),null==(t=o.byobRequest)||t.respond(0),null==(r=n.byobRequest)||r.respond(0),b&&p||i(void 0),null;let l=e.value,s=l;if(!b&&!p)try{s=et(l)}catch(e){return o.error(e),n.error(e),i(a.cancel(e)),null}return b||o.enqueue(l),p||n.enqueue(s),d=!1,f?v():h&&w(),null},()=>(d=!1,null))}function g(t,r){s||(a.releaseLock(),y(a=e.getReader({mode:"byob"})),s=!0);let l=r?n:o,u=r?o:n;c(a.read(t),e=>{var t;f=!1,h=!1;let o=r?p:b,n=r?b:p;if(e.done){o||l.close(),n||u.close();let r=e.value;return void 0!==r&&(o||l.byobRequest.respondWithNewView(r),n||null==(t=u.byobRequest)||t.respond(0)),o&&n||i(void 0),null}let s=e.value;if(n)o||l.byobRequest.respondWithNewView(s);else{let e;try{e=et(s)}catch(e){return l.error(e),u.error(e),i(a.cancel(e)),null}o||l.byobRequest.respondWithNewView(s),u.enqueue(e)}return d=!1,f?v():h&&w(),null},()=>(d=!1,null))}function v(){if(d)return f=!0,l(void 0);d=!0;let e=o.byobRequest;return null===e?m():g(e.view,!1),l(void 0)}function w(){if(d)return h=!0,l(void 0);d=!0;let e=n.byobRequest;return null===e?m():g(e.view,!0),l(void 0)}let S=new tw({type:"bytes",start(e){o=e},pull:v,cancel:function(e){if(b=!0,t=e,p){let e=[t,r],o=a.cancel(e);i(o)}return _}}),R=new tw({type:"bytes",start(e){n=e},pull:w,cancel:function(e){if(p=!0,r=e,b){let e=[t,r],o=a.cancel(e);i(o)}return _}});return y(a),[S,R]}(this)}values(e){if(!I(this))throw tP("values");return function(e,t){let r=new G(e.getReader(),t),o=Object.create(X);return o._asyncIteratorImpl=r,o}(this,(O(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}}function tS(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof tw}function tR(e){return void 0!==e._reader}function tT(e,t){if(e._disturbed=!0,"closed"===e._state)return l(void 0);if("errored"===e._state)return s(e._storedError);tq(e);let o=e._reader;if(void 0!==o&&ej(o)){let e=o._readIntoRequests;o._readIntoRequests=new _,e.forEach(e=>{e._closeSteps(void 0)})}return d(e._readableStreamController[g](t),r,void 0)}function tq(e){e._state="closed";let t=e._reader;if(void 0!==t&&(k(t),Q(t))){let e=t._readRequests;t._readRequests=new _,e.forEach(e=>{e._closeSteps()})}}function tE(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(P(r,t),Q(r)?U(r,t):ez(r,t))}function tP(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function tk(e,t){O(e,t);let r=null==e?void 0:e.highWaterMark;return F(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:B(r)}}Object.defineProperties(tw.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),n(tw.prototype.cancel,"cancel"),n(tw.prototype.getReader,"getReader"),n(tw.prototype.pipeThrough,"pipeThrough"),n(tw.prototype.pipeTo,"pipeTo"),n(tw.prototype.tee,"tee"),n(tw.prototype.values,"values"),"symbol"==typeof t.toStringTag&&Object.defineProperty(tw.prototype,t.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof t.asyncIterator&&Object.defineProperty(tw.prototype,t.asyncIterator,{value:tw.prototype.values,writable:!0,configurable:!0});let tC=e=>e.byteLength;n(tC,"size");class tW{constructor(e){z(e,1,"ByteLengthQueuingStrategy"),e=tk(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!tA(this))throw tO("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!tA(this))throw tO("size");return tC}}function tO(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function tA(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof tW}Object.defineProperties(tW.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tW.prototype,t.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let tj=()=>1;n(tj,"size");class tz{constructor(e){z(e,1,"CountQueuingStrategy"),e=tk(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!tB(this))throw tF("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!tB(this))throw tF("size");return tj}}function tF(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function tB(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof tz}Object.defineProperties(tz.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tz.prototype,t.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class tL{constructor(e={},t={},r={}){let o;void 0===e&&(e=null);let n=eI(t,"Second parameter"),i=eI(r,"Third parameter"),a=function(e,t){O(e,t);let r=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,i=null==e?void 0:e.transform,a=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:(A(r,`${t} has member 'flush' that`),t=>p(r,e,[t])),readableType:o,start:void 0===n?void 0:(A(n,`${t} has member 'start' that`),t=>b(n,e,[t])),transform:void 0===i?void 0:(A(i,`${t} has member 'transform' that`),(t,r)=>p(i,e,[t,r])),writableType:a}}(e,"First parameter");if(void 0!==a.readableType)throw RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw RangeError("Invalid writableType specified");let c=eB(i,0),f=eL(i),h=eB(n,1),_=eL(n);!function(e,t,r,o,n,i){var a,s,u;function c(){return t}e._writableState="writable",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,a=function(t){let r=e._transformStreamController;return e._backpressure?d(e._backpressureChangePromise,()=>{if("erroring"===(ex(e._writable)?e._writable._state:e._writableState))throw ex(e._writable)?e._writable._storedError:e._writableStoredError;return tQ(r,t)},void 0):tQ(r,t)},s=function(){var t=e;let r=t._transformStreamController,o=r._flushAlgorithm();return tH(r),d(o,()=>{if("errored"===t._readableState)throw t._readableStoredError;tG(t)&&tX(t)},e=>{throw tM(t,e),t._readableStoredError})},e._writable=new e$({start(t){e._writableController=t;try{let r=t.signal;void 0!==r&&r.addEventListener("abort",()=>{"writable"===e._writableState&&(e._writableState="erroring",r.reason&&(e._writableStoredError=r.reason))})}catch(e){}return d(c(),()=>(e._writableStarted=!0,t3(e),null),t=>{throw e._writableStarted=!0,tZ(e,t),t})},write:t=>(e._writableHasInFlightOperation=!0,d(a(t),()=>(e._writableHasInFlightOperation=!1,t3(e),null),t=>{throw e._writableHasInFlightOperation=!1,tZ(e,t),t})),close:()=>(e._writableHasInFlightOperation=!0,d(s(),()=>(e._writableHasInFlightOperation=!1,"erroring"===e._writableState&&(e._writableStoredError=void 0),e._writableState="closed",null),t=>{throw e._writableHasInFlightOperation=!1,e._writableState,tZ(e,t),t})),abort:t=>(e._writableState="errored",e._writableStoredError=t,function(t){return tM(e,t),l(void 0)}(t))},{highWaterMark:r,size:o}),e._readableState="readable",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,u=function(){return tx(e,!1),e._backpressureChangePromise},e._readable=new tw({start:t=>(e._readableController=t,c().catch(t=>{tJ(e,t)})),pull:()=>(e._readablePulling=!0,u().catch(t=>{tJ(e,t)})),cancel:t=>(e._readableState="closed",function(t){return t$(e,t),l(void 0)}(t))},{highWaterMark:n,size:i}),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,tx(e,!0),e._transformStreamController=void 0}(this,u(e=>{o=e}),h,_,c,f),function(e,t){let r,o,n=Object.create(tN.prototype);r=void 0!==t.transform?e=>t.transform(e,n):e=>{try{var t;return tV(n,e),t=void 0,l(t)}catch(e){return s(e)}},o=void 0!==t.flush?()=>t.flush(n):()=>l(void 0),n._controlledTransformStream=e,e._transformStreamController=n,n._transformAlgorithm=r,n._flushAlgorithm=o}(this,a),void 0!==a.start?o(a.start(this._transformStreamController)):o(void 0)}get readable(){if(!tI(this))throw tY("readable");return this._readable}get writable(){if(!tI(this))throw tY("writable");return this._writable}}function tI(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof tL}function tM(e,t){tJ(e,t),t$(e,t)}function t$(e,t){tH(e._transformStreamController),e._writableController.error(t),"writable"===e._writableState&&t0(e,t),e._backpressure&&tx(e,!1)}function tx(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=u(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(tL.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tL.prototype,t.toStringTag,{value:"TransformStream",configurable:!0});class tN{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tD(this))throw tU("desiredSize");return tK(this._controlledTransformStream)}enqueue(e){if(!tD(this))throw tU("enqueue");tV(this,e)}error(e){if(!tD(this))throw tU("error");tM(this._controlledTransformStream,e)}terminate(){if(!tD(this))throw tU("terminate");let e=this._controlledTransformStream;tG(e)&&tX(e),t$(e,TypeError("TransformStream terminated"))}}function tD(e){return!!o(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof tN}function tH(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function tV(e,t){let r=e._controlledTransformStream;if(!tG(r))throw TypeError("Readable side is not in a state that permits enqueue");try{r._readablePulling=!1;try{r._readableController.enqueue(t)}catch(e){throw tJ(r,e),e}}catch(e){throw t$(r,e),r._readableStoredError}!(tG(r)&&(r._readablePulling||tK(r)>0))!==r._backpressure&&tx(r,!0)}function tQ(e,t){return d(e._transformAlgorithm(t),void 0,t=>{throw tM(e._controlledTransformStream,t),t})}function tU(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function tY(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}function tG(e){return!e._readableCloseRequested&&"readable"===e._readableState}function tX(e){e._readableState="closed",e._readableCloseRequested=!0,e._readableController.close()}function tJ(e,t){"readable"===e._readableState&&(e._readableState="errored",e._readableStoredError=t),e._readableController.error(t)}function tK(e){return e._readableController.desiredSize}function tZ(e,t){"writable"!==e._writableState?t1(e):t0(e,t)}function t0(e,t){e._writableState="erroring",e._writableStoredError=t,!e._writableHasInFlightOperation&&e._writableStarted&&t1(e)}function t1(e){e._writableState="errored"}function t3(e){"erroring"===e._writableState&&t1(e)}Object.defineProperties(tN.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),n(tN.prototype.enqueue,"enqueue"),n(tN.prototype.error,"error"),n(tN.prototype.terminate,"terminate"),"symbol"==typeof t.toStringTag&&Object.defineProperty(tN.prototype,t.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),e.s(["isFunction",()=>t8],93633);let t8=e=>"function"==typeof e;async function*t6(e){let t=e.byteOffset+e.byteLength,r=e.byteOffset;for(;r!==t;){let o=Math.min(t-r,65536),n=e.buffer.slice(r,r+o);r+=n.byteLength,yield new Uint8Array(n)}}async function*t4(e){let t=0;for(;t!==e.size;){let r=e.slice(t,Math.min(e.size,t+65536)),o=await r.arrayBuffer();t+=o.byteLength,yield new Uint8Array(o)}}async function*t5(e,t=!1){for(let r of e)ArrayBuffer.isView(r)?t?yield*t6(r):yield r:t8(r.stream)?yield*r.stream():yield*t4(r)}var t9,t7,t2,re,rt,rr=function(e,t,r,o){if("a"===r&&!o)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?o:"a"===r?o.call(e):o?o.value:t.get(e)},ro=function(e,t,r,o,n){if("m"===o)throw TypeError("Private method is not writable");if("a"===o&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===o?n.call(e,r):n?n.value=r:t.set(e,r),r};class rn{constructor(e=[],t={}){if(t9.set(this,[]),t7.set(this,""),t2.set(this,0),null!=t||(t={}),"object"!=typeof e||null===e)throw TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(!t8(e[Symbol.iterator]))throw TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if("object"!=typeof t&&!t8(t))throw TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");let r=new TextEncoder;for(let t of e){let e;e=ArrayBuffer.isView(t)?new Uint8Array(t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)):t instanceof ArrayBuffer?new Uint8Array(t.slice(0)):t instanceof rn?t:r.encode(String(t)),ro(this,t2,rr(this,t2,"f")+(ArrayBuffer.isView(e)?e.byteLength:e.size),"f"),rr(this,t9,"f").push(e)}let o=void 0===t.type?"":String(t.type);ro(this,t7,/^[\x20-\x7E]*$/.test(o)?o:"","f")}static[(t9=new WeakMap,t7=new WeakMap,t2=new WeakMap,Symbol.hasInstance)](e){return!!(e&&"object"==typeof e&&t8(e.constructor)&&(t8(e.stream)||t8(e.arrayBuffer))&&/^(Blob|File)$/.test(e[Symbol.toStringTag]))}get type(){return rr(this,t7,"f")}get size(){return rr(this,t2,"f")}slice(e,t,r){return new rn(function*(e,t,r=0,o){null!=o||(o=t);let n=r<0?Math.max(t+r,0):Math.min(r,t),i=o<0?Math.max(t+o,0):Math.min(o,t),a=Math.max(i-n,0),l=0;for(let t of e){if(l>=a)break;let e=ArrayBuffer.isView(t)?t.byteLength:t.size;if(n&&e<=n)n-=e,i-=e;else{let r;ArrayBuffer.isView(t)?l+=(r=t.subarray(n,Math.min(e,i))).byteLength:l+=(r=t.slice(n,Math.min(e,i))).size,i-=e,n=0,yield r}}}(rr(this,t9,"f"),this.size,e,t),{type:r})}async text(){let e=new TextDecoder,t="";for await(let r of t5(rr(this,t9,"f")))t+=e.decode(r,{stream:!0});return t+e.decode()}async arrayBuffer(){let e=new Uint8Array(this.size),t=0;for await(let r of t5(rr(this,t9,"f")))e.set(r,t),t+=r.length;return e.buffer}stream(){let e=t5(rr(this,t9,"f"),!0);return new tw({async pull(t){let{value:r,done:o}=await e.next();if(o)return queueMicrotask(()=>t.close());t.enqueue(r)},async cancel(){await e.return()}})}get[Symbol.toStringTag](){return"Blob"}}Object.defineProperties(rn.prototype,{type:{enumerable:!0},size:{enumerable:!0},slice:{enumerable:!0},stream:{enumerable:!0},text:{enumerable:!0},arrayBuffer:{enumerable:!0}});var ri=function(e,t,r,o,n){if("m"===o)throw TypeError("Private method is not writable");if("a"===o&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===o?n.call(e,r):n?n.value=r:t.set(e,r),r},ra=function(e,t,r,o){if("a"===r&&!o)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?o:"a"===r?o.call(e):o?o.value:t.get(e)};class rl extends rn{constructor(e,t,r={}){if(super(e,r),re.set(this,void 0),rt.set(this,0),arguments.length<2)throw TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);ri(this,re,String(t),"f");let o=void 0===r.lastModified?Date.now():Number(r.lastModified);Number.isNaN(o)||ri(this,rt,o,"f")}static[(re=new WeakMap,rt=new WeakMap,Symbol.hasInstance)](e){return e instanceof rn&&"File"===e[Symbol.toStringTag]&&"string"==typeof e.name}get name(){return ra(this,re,"f")}get lastModified(){return ra(this,rt,"f")}get webkitRelativePath(){return""}get[Symbol.toStringTag](){return"File"}}e.s(["isFile",()=>rs],54769);let rs=e=>e instanceof rl}];

//# sourceMappingURL=node_modules_formdata-node_lib_esm_File_ccf0a600.js.map