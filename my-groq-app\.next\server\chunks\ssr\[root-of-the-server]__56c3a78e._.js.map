{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/node_modules/next/src/client/components/handle-isr-error.tsx", "turbopack:///[project]/node_modules/next/src/client/components/builtin/global-error.tsx", "turbopack:///[project]/src/app/page.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n", "'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n", "'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\n\ninterface Message {\n  id: string;\n  role: 'user' | 'assistant';\n  content: string;\n}\n\nexport default function Chat() {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: input,\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInput('');\n    setIsLoading(true);\n\n    const assistantMessage: Message = {\n      id: (Date.now() + 1).toString(),\n      role: 'assistant',\n      content: '',\n    };\n\n    setMessages(prev => [...prev, assistantMessage]);\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          messages: [...messages, userMessage],\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch response');\n      }\n\n      const reader = response.body?.getReader();\n      const decoder = new TextDecoder();\n\n      if (reader) {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n\n          const chunk = decoder.decode(value);\n          const lines = chunk.split('\\n');\n\n          for (const line of lines) {\n            if (line.startsWith('data: ')) {\n              const data = line.slice(6);\n              if (data === '[DONE]') break;\n\n              try {\n                const parsed = JSON.parse(data);\n                if (parsed.content) {\n                  setMessages(prev => prev.map(msg =>\n                    msg.id === assistantMessage.id\n                      ? { ...msg, content: msg.content + parsed.content }\n                      : msg\n                  ));\n                }\n              } catch (e) {\n                // Ignore parsing errors for non-JSON lines\n              }\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error:', error);\n      setMessages(prev => prev.map(msg =>\n        msg.id === assistantMessage.id\n          ? { ...msg, content: 'Sorry, I encountered an error. Please try again.' }\n          : msg\n      ));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"mx-auto w-full max-w-4xl py-8 px-4\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 mb-2\">\n            PIP FTUI\n          </h1>\n          <p className=\"text-gray-600 mb-4\">\n            Powered by Llama 3.1 8B Instant\n          </p>\n          {/* Mode Switcher */}\n          <div className=\"flex justify-center gap-4 mt-4\">\n            <div className=\"bg-blue-500 text-white px-6 py-2 rounded-lg font-medium\">\n              💬 Chat Mode\n            </div>\n            <Link href=\"/rag\">\n              <div className=\"bg-white text-gray-700 border-2 border-gray-300 px-6 py-2 rounded-lg font-medium hover:border-purple-500 hover:text-purple-600 transition-colors cursor-pointer\">\n                📚 RAG Mode\n              </div>\n            </Link>\n          </div>\n        </div>\n\n        {/* Chat Messages */}\n        <div className=\"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200\">\n          <div className=\"p-6 space-y-6\">\n            {messages.length === 0 ? (\n              <div className=\"text-center text-gray-500 mt-8\">\n                <div className=\"text-6xl mb-4\">💬</div>\n                <p className=\"text-lg\">Start a conversation!</p>\n                <p className=\"text-sm\">Type your message below to begin chatting.</p>\n              </div>\n            ) : (\n              messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n                >\n                  <div\n                    className={`\n                      max-w-[80%] rounded-2xl px-6 py-4 shadow-md\n                      ${message.role === 'user'\n                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'\n                        : 'bg-gray-100 text-gray-800 border border-gray-200'}\n                    `}\n                  >\n                    <div className={`text-xs mb-2 font-medium ${\n                      message.role === 'user' ? 'text-blue-100' : 'text-gray-500'\n                    }`}>\n                      {message.role === 'user' ? 'You' : 'Llama 3.1 8B Instant'}\n                    </div>\n                    <div className={`text-sm leading-relaxed whitespace-pre-wrap ${\n                      message.role === 'user' ? 'text-white' : 'text-gray-700'\n                    }`}>\n                      {message.content}\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n            {isLoading && (\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gray-100 rounded-2xl px-6 py-4 shadow-md border border-gray-200\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                    </div>\n                    <span className=\"text-sm text-gray-500\">Thinking...</span>\n                  </div>\n                </div>\n              </div>\n            )}\n            <div ref={messagesEndRef} />\n          </div>\n        </div>\n\n        {/* Input Form */}\n        <form onSubmit={handleSubmit} className=\"flex gap-4\">\n          <div className=\"flex-1 relative\">\n            <input\n              value={input}\n              onChange={(e) => setInput(e.target.value)}\n              placeholder=\"Type your message...\"\n              disabled={isLoading}\n              className=\"w-full rounded-2xl border-2 border-gray-200 px-6 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 disabled:bg-gray-50 disabled:cursor-not-allowed text-lg shadow-lg\"\n            />\n          </div>\n          <button\n            type=\"submit\"\n            disabled={isLoading || !input.trim()}\n            className=\"rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 px-8 py-4 text-white font-medium hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-200\"\n          >\n            {isLoading ? 'Sending...' : 'Send'}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": ["module", "exports", "require", "vendored", "React", "AppRouterContext", "ReactServerDOMTurbopackClient", "HooksClientContext", "ReactDOM", "ServerInsertedHtml", "process", "env", "NEXT_RUNTIME", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "ReactJsxRuntime", "HandleISRError", "workAsyncStorage", "window", "undefined", "error", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p"], "mappings": "6CAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK,8BCF9BJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACE,gBAAgB,8BCFvCL,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEG,6BAA6B,8BCFtDN,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACI,kBAAkB,+BCFzCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEK,QAAQ,+BCFjCR,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACM,kBAAkB,+sBCwBjCT,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEa,eAAe,wGCQxBC,iBAAAA,qCAAAA,KAVhB,IAAMC,EAGEhB,EAAQ,CAAA,CAAA,IAAA,GACRgB,MAHN,OAAOC,GAGe,CAMjB,EALDC,KAJc,EASJH,EAAe,CAAyB,EAAzB,GAAA,OAAEI,CAAK,CAAkB,CAAzB,EAC7B,GAAIH,EAAkB,CACpB,IAAMI,EAAQJ,EAAiBK,QAAQ,GACvC,GAAID,CAAAA,QAAAA,KAAAA,EAAAA,EAAOE,YAAY,AAAZA,IAAgBF,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAOG,kBAAAA,AAAkB,EAElD,CAFoD,KACpDC,QAAQL,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,+TCgCA,OADA,AADA,GAEA,qCAAA,GAD2C,uBAjDZ,CAAA,CAAA,IAAA,GAEzBM,EAAS,CACbN,EA6C8E,IA7CvE,CAELO,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA8BA,EAzBA,SAASC,AAAmB,AAyBbA,CAzBsC,EAAzB,GAAA,CAAEnB,OAAK,CAAkB,CAAzB,EACpBoB,EAA6BpB,MAAAA,EAAAA,KAAAA,EAAAA,EAAOoB,MAAM,CAChD,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAACC,CADH,MACGA,CAAKC,GAAG,2BACP,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAACC,OAAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC5B,EAAAA,cAAc,CAAA,CAACI,MAAOA,IACvB,CAAA,EAAA,EAAA,GAAA,EAACyB,MAAAA,CAAIC,MAAOpB,EAAON,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACyB,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,IAAA,EAACE,KAAAA,CAAGD,MAAOpB,EAAOQ,IAAI,WAAE,wBACAM,EAAS,SAAW,SAAS,8CACvBtB,OAAO8B,QAAQ,CAACC,QAAQ,CAAC,YAAU,IAC9DT,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAS,CAAA,EAAA,EAAA,EAATA,CAAS,EAACU,IAAAA,CAAEJ,GAAZN,GAAmBd,EAAOQ,IAAI,UAAI,WAAUM,IAAgB,eAMzE,yRC9CA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAQe,SAAS,IACtB,GAAM,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,EAAiB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAiB,MAM9C,CAAA,EAAA,EAAA,SAAS,AAAT,EAAU,KAHR,EAAe,OAAO,EAAE,eAAe,CAAE,SAAU,QAAS,EAK9D,EAAG,CAAC,EAAS,EAEb,IAAM,EAAe,MAAO,IAE1B,GADA,EAAE,cAAc,GACZ,CAAC,EAAM,IAAI,IAAM,EAAW,OAEhC,IAAM,EAAuB,CAC3B,GAAI,KAAK,GAAG,GAAG,QAAQ,GACvB,KAAM,OACN,QAAS,CACX,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAY,EAC1C,EAAS,IACT,GAAa,GAEb,IAAM,EAA4B,CAChC,GAAI,CAAC,KAAK,GAAG,GAAK,CAAC,EAAE,QAAQ,GAC7B,KAAM,YACN,QAAS,EACX,EAEA,EAAY,GAAQ,IAAI,EAAM,EAAiB,EAE/C,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,YAAa,CACxC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,IAAI,EAAU,EAAY,AACtC,EACF,GAEA,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACV,AAAI,MAAM,4BAGlB,IAAM,EAAS,EAAS,IAAI,EAAE,YACxB,EAAU,IAAI,YAEpB,GAAI,EACF,MADU,AACH,CAAM,CACX,GAAM,MAAE,CAAI,OAAE,CAAK,CAAE,CAAG,MAAM,EAAO,IAAI,GACzC,GAAI,EAAM,MAKV,IAAK,IAAM,KAHG,AACA,EADQ,CAGH,KAHS,CAAC,AAGH,GAFN,KAAK,CAAC,MAGxB,GAAI,EAAK,UAAU,CAAC,UAAW,CAC7B,IAAM,EAAO,EAAK,KAAK,CAAC,GACxB,GAAa,WAAT,EAAmB,MAEvB,GAAI,CACF,IAAM,EAAS,KAAK,KAAK,CAAC,EACtB,GAAO,OAAO,EAAE,AAClB,EAAY,GAAQ,EAAK,GAAG,CAAC,GAC3B,EAAI,EAAE,GAAK,EAAiB,EAAE,CAC1B,CAAE,GAAG,CAAG,CAAE,QAAS,EAAI,OAAO,CAAG,EAAO,OAAO,AAAC,EAChD,GAGV,CAAE,MAAO,EAAG,CAEZ,CACF,CAEJ,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,SAAU,GACxB,EAAY,GAAQ,EAAK,GAAG,CAAC,GAC3B,EAAI,EAAE,GAAK,EAAiB,EAAE,CAC1B,CAAE,GAAG,CAAG,CAAE,QAAS,kDAAmD,EACtE,GAER,QAAU,CACR,GAAa,EACf,CACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qEACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+CAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,iDAAwC,aAGtD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,oCAIlC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mEAA0D,iBAGzE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,gBACT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2KAAkK,wBAQvL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACQ,IAApB,EAAS,MAAM,CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,0BACvB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAU,kDAGzB,EAAS,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,UAAW,CAAC,KAAK,EAAmB,SAAjB,EAAQ,IAAI,CAAc,cAAgB,gBAAA,CAAiB,UAE9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAW,CAAC;;sBAEV,EAAmB,SAAjB,EAAQ,IAAI,CACV,wDACA,mDAAmD;oBACzD,CAAC,WAED,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,yBAAyB,EACvB,SAAjB,EAAQ,IAAI,CAAc,gBAAkB,gBAAA,CAC5C,UACkB,SAAjB,EAAQ,IAAI,CAAc,MAAQ,yBAErC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,4CAA4C,EAC1C,AAAjB,WAAQ,IAAI,CAAc,aAAe,gBAAA,CACzC,UACC,EAAQ,OAAO,OAnBf,EAAQ,EAAE,GAyBpB,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAkD,MAAO,CAAE,eAAgB,MAAO,IACjG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDAAkD,MAAO,CAAE,eAAgB,MAAO,OAEnG,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iCAAwB,uBAKhD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,IAAK,SAKd,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAc,UAAU,uBACtC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAS,EAAE,MAAM,CAAC,KAAK,EACxC,YAAY,uBACZ,SAAU,EACV,UAAU,2OAGd,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,SAAU,GAAa,CAAC,EAAM,IAAI,GAClC,UAAU,uSAET,EAAY,aAAe,gBAMxC", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}