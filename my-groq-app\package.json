{"name": "my-groq-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start"}, "dependencies": {"@ai-sdk/groq": "^2.0.22", "ai": "^5.0.59", "groq-sdk": "^0.33.0", "next": "15.5.4", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}