<!DOCTYPE html><!--Ilqwh5vGIgZMaF5Xex4Un--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/4a7551bcc3548e67-s.p.717db902.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/7f20430e44eb7422-s.p.bd5bbcc6.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/chunks/368f982dbe22dc4a.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/6420740671896b80.js"/><script src="/_next/static/chunks/569f8ca39997ccda.js" async=""></script><script src="/_next/static/chunks/150316a471952cee.js" async=""></script><script src="/_next/static/chunks/3e0f93b37bf948e9.js" async=""></script><script src="/_next/static/chunks/turbopack-66a3cd7079ccda69.js" async=""></script><script src="/_next/static/chunks/37f79cc3a1d4c533.js" async=""></script><script src="/_next/static/chunks/ff1a16fafef87110.js" async=""></script><script src="/_next/static/chunks/7dd66bdf8a7e5707.js" async=""></script><script src="/_next/static/chunks/71c3a2dad7e1a71d.js" async=""></script><script src="/_next/static/chunks/2e174c49e87b624b.js" async=""></script><meta name="next-size-adjust" content=""/><title>PIP FTUI - Pusat Informasi Publik</title><meta name="description" content="AI Assistant untuk Informasi dan Layanan Fakultas Teknik Universitas Indonesia"/><link rel="icon" href="/favicon.ico?favicon.0b3bf435.ico" sizes="256x256" type="image/x-icon"/><script src="/_next/static/chunks/a6dad97d9634a72d.js" noModule=""></script></head><body class="quicksand_b7e087bf-module__3kZyda__variable comfortaa_5e8afc88-module__lBtixG__variable antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen flex flex-col" style="background:linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"><nav class="fixed top-0 left-0 right-0 z-50 transition-colors duration-300 bg-[#2d5a9e]"><div class="px-8 py-3 shadow-lg"><div class="flex items-center justify-between w-full"><a class="flex items-center hover:opacity-90 transition-opacity" href="/"><img alt="PIP FTUI Logo" loading="lazy" width="160" height="55" decoding="async" data-nimg="1" class="h-12 w-auto" style="color:transparent" src="/Landing_Page/PIP LOGO 2.svg"/></a><div class="flex items-center gap-8"><button class="relative w-14 h-7 bg-white/20 rounded-full p-0.5 cursor-pointer transition-colors hover:bg-white/30" aria-label="Toggle theme"><div class="absolute top-0.5 left-0.5 w-6 h-6 rounded-full bg-white shadow-md transform transition-transform duration-300 ease-in-out flex items-center justify-center translate-x-0"><span class="text-sm">🌙</span></div></button><a class="text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors" href="/home">Home</a><a class="text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors" href="/documents">Documents</a><a class="text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors" href="/academics">Academics</a><a class="text-white font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-300 transition-colors" href="/contacts">Contacts</a><a class="text-yellow-300 font-[family-name:var(--font-comfortaa)] font-bold text-lg hover:text-yellow-400 transition-colors border-2 border-yellow-300 px-4 py-1 rounded-lg" href="/prototypetesting">🧪 Prototype</a></div></div></div></nav><div class="mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow"><div class="text-center mb-8"><div><h1 class="text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]">PIP FTUI - RAG Mode</h1><h2 class="text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]">Retrieval-Augmented Generation</h2></div><p class="text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]">Cari informasi FTUI berdasarkan dokumen yang tersedia</p><div class="flex justify-center gap-4 mt-6"><a href="/chat"><div class="bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]">💬 Chat Mode</div></a><div class="bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]">📚 RAG Mode</div></div></div><div class="flex gap-6"><div class="w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200"><div class="flex justify-between items-center mb-4"><h2 class="text-xl font-bold text-gray-800">Documents</h2><button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">+ Upload</button></div><div class="overflow-y-auto h-[calc(100%-100px)]"><div class="text-center text-gray-500 mt-8"><div class="text-4xl mb-2">📄</div><p class="text-sm">No documents yet</p><p class="text-xs text-gray-400 mt-1">Upload a document to start</p></div></div></div><div class="flex-1"><div class="bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200"><div class="p-6 space-y-6"><div class="text-center text-[#346ad5] mt-8"><div class="text-6xl mb-4">📚</div><p class="text-lg font-semibold">Tanyakan tentang informasi FTUI</p><p class="text-sm">Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan.</p></div></div></div><form class="relative flex-shrink-0"><div class="bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4"><input placeholder="ASK PIP..." disabled="" class="flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed" value=""/><button type="submit" disabled="" class="ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]" aria-label="Send message"><svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path></svg></button></div></form></div></div></div><footer class="text-white mt-auto transition-colors duration-300 bg-[#2d5a9e]"><div class="px-8 py-8"><div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-8 w-full"><div class="flex flex-col gap-3"><img alt="PIP FTUI Logo" loading="lazy" width="220" height="80" decoding="async" data-nimg="1" class="h-20 w-auto" style="color:transparent" src="/Landing_Page/PIP LOGO 2.svg"/><p class="text-white font-[family-name:var(--font-comfortaa)] text-base font-semibold">@ PIP All Rights Reserved.</p></div><div class="flex flex-col items-start md:items-end gap-4"><div class="flex items-start gap-2 text-white"><img alt="Location Pin" loading="lazy" width="20" height="20" decoding="async" data-nimg="1" class="mt-0.5 flex-shrink-0" style="color:transparent" srcSet="/_next/image?url=%2FFooter%2Fmarker-pin-02.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2FFooter%2Fmarker-pin-02.png&amp;w=48&amp;q=75 2x" src="/_next/image?url=%2FFooter%2Fmarker-pin-02.png&amp;w=48&amp;q=75"/><p class="text-sm font-[family-name:var(--font-comfortaa)] max-w-md text-left md:text-right leading-relaxed">Pusgiwa UI, Gedung D Lt. 7, Jl. Prof. Dr. Fuad Hassan, Kukusan, Kecamatan Beji, Kota Depok, Jawa Barat 16425</p></div><div class="flex items-center gap-3"><a target="_blank" class="w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity" aria-label="Instagram" href="https://instagram.com"><img alt="Instagram" loading="lazy" width="36" height="36" decoding="async" data-nimg="1" class="w-full h-full" style="color:transparent" srcSet="/_next/image?url=%2FFooter%2Finstagram%201.png&amp;w=48&amp;q=75 1x, /_next/image?url=%2FFooter%2Finstagram%201.png&amp;w=96&amp;q=75 2x" src="/_next/image?url=%2FFooter%2Finstagram%201.png&amp;w=96&amp;q=75"/></a><a target="_blank" class="w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity" aria-label="LinkedIn" href="https://linkedin.com"><img alt="LinkedIn" loading="lazy" width="36" height="36" decoding="async" data-nimg="1" class="w-full h-full" style="color:transparent" srcSet="/_next/image?url=%2FFooter%2Flinkedin%201.png&amp;w=48&amp;q=75 1x, /_next/image?url=%2FFooter%2Flinkedin%201.png&amp;w=96&amp;q=75 2x" src="/_next/image?url=%2FFooter%2Flinkedin%201.png&amp;w=96&amp;q=75"/></a><a target="_blank" class="w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity" aria-label="YouTube" href="https://youtube.com"><img alt="YouTube" loading="lazy" width="36" height="36" decoding="async" data-nimg="1" class="w-full h-full" style="color:transparent" srcSet="/_next/image?url=%2FFooter%2Fyoutube%201.png&amp;w=48&amp;q=75 1x, /_next/image?url=%2FFooter%2Fyoutube%201.png&amp;w=96&amp;q=75 2x" src="/_next/image?url=%2FFooter%2Fyoutube%201.png&amp;w=96&amp;q=75"/></a><a target="_blank" class="w-9 h-9 flex items-center justify-center hover:opacity-80 transition-opacity" aria-label="Facebook" href="https://facebook.com"><img alt="Facebook" loading="lazy" width="36" height="36" decoding="async" data-nimg="1" class="w-full h-full" style="color:transparent" srcSet="/_next/image?url=%2FFooter%2Ffacebook%201.png&amp;w=48&amp;q=75 1x, /_next/image?url=%2FFooter%2Ffacebook%201.png&amp;w=96&amp;q=75 2x" src="/_next/image?url=%2FFooter%2Ffacebook%201.png&amp;w=96&amp;q=75"/></a></div></div></div></div></footer></div><!--$--><!--/$--><script src="/_next/static/chunks/6420740671896b80.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[30824,[\"/_next/static/chunks/37f79cc3a1d4c533.js\"],\"default\"]\n3:I[39756,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"default\"]\n4:I[37457,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"default\"]\n5:I[4322,[\"/_next/static/chunks/37f79cc3a1d4c533.js\",\"/_next/static/chunks/71c3a2dad7e1a71d.js\",\"/_next/static/chunks/2e174c49e87b624b.js\"],\"default\"]\n6:I[97367,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"OutletBoundary\"]\n8:I[11533,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"AsyncMetadataOutlet\"]\na:I[97367,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"ViewportBoundary\"]\nc:I[97367,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"MetadataBoundary\"]\nd:\"$Sreact.suspense\"\nf:I[68027,[\"/_next/static/chunks/37f79cc3a1d4c533.js\"],\"default\"]\n:HL[\"/_next/static/chunks/368f982dbe22dc4a.css\",\"style\"]\n:HL[\"/_next/static/media/4a7551bcc3548e67-s.p.717db902.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/7f20430e44eb7422-s.p.bd5bbcc6.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"Ilqwh5vGIgZMaF5Xex4Un\",\"p\":\"\",\"c\":[\"\",\"rag\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"rag\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/chunks/368f982dbe22dc4a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"script\",\"script-0\",{\"src\":\"/_next/static/chunks/37f79cc3a1d4c533.js\",\"async\":true,\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"quicksand_b7e087bf-module__3kZyda__variable comfortaa_5e8afc88-module__lBtixG__variable antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"rag\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{}],[[\"$\",\"script\",\"script-0\",{\"src\":\"/_next/static/chunks/71c3a2dad7e1a71d.js\",\"async\":true,\"nonce\":\"$undefined\"}],[\"$\",\"script\",\"script-1\",{\"src\":\"/_next/static/chunks/2e174c49e87b624b.js\",\"async\":true,\"nonce\":\"$undefined\"}]],[\"$\",\"$L6\",null,{\"children\":[\"$L7\",[\"$\",\"$L8\",null,{\"promise\":\"$@9\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$d\",null,{\"fallback\":null,\"children\":\"$Le\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$f\",[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/chunks/368f982dbe22dc4a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n7:null\n"])</script><script>self.__next_f.push([1,"10:I[27201,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"IconMark\"]\n9:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"PIP FTUI - Pusat Informasi Publik\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"AI Assistant untuk Informasi dan Layanan Fakultas Teknik Universitas Indonesia\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico?favicon.0b3bf435.ico\",\"sizes\":\"256x256\",\"type\":\"image/x-icon\"}],[\"$\",\"$L10\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"e:\"$9:metadata\"\n"])</script></body></html>