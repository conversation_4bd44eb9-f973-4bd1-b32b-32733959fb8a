# Quick Start Guide

## 🚀 Quick Setup (5 menit)

### 1️⃣ Install Backend Dependencies

```powershell
cd backend
pip install -r requirements.txt
```

### 2️⃣ Jalankan Backend

```powershell
# Dari folder backend
python main.py
```

Backend akan ber<PERSON>lan di **http://localhost:8000**

### 3️⃣ Jalankan Frontend (Terminal Baru)

```powershell
# Dari folder my-groq-app
cd ..\my-groq-app
npm run dev
```

Frontend akan berjalan di **http://localhost:3000**

### 4️⃣ Buka Browser

Buka **http://localhost:3000** dan coba:
- **Chat Mode**: Chat langsung
- **RAG Mode**: Upload dokumen dan tanya

---

## 📝 Contoh Penggunaan RAG

1. Klik "📚 RAG Mode"
2. Klik "+ Upload"
3. Upload file PDF/TXT/DOCX
4. Tunggu proses upload
5. Ketik pertanyaan, contoh:
   - "Apa isi utama dokumen ini?"
   - "Jelaskan tentang [topik] di dokumen"
   - "Berikan ringkasan dokumen"

---

## ⚡ One-Command Setup

### PowerShell Script (Coming Soon)

```powershell
# setup.ps1 - Akan dibuat untuk auto-setup
.\setup.ps1
```

---

## 🔧 Troubleshooting Cepat

### Backend Error

```powershell
# Reinstall dependencies
pip install --upgrade -r requirements.txt

# Atau install torch manual
pip install torch --index-url https://download.pytorch.org/whl/cpu
```

### Frontend Error

```powershell
# Reinstall node_modules
rm -rf node_modules
npm install
```

### Port sudah digunakan

```powershell
# Backend - ubah port di main.py baris terakhir:
# uvicorn.run(app, host="0.0.0.0", port=8001)

# Frontend - ubah di package.json:
# "dev": "next dev -p 3001"
```

---

## 📚 Dokumentasi Lengkap

Lihat `README.md` untuk dokumentasi lengkap.
