<!DOCTYPE html><!--ha4rHZlxg3udBiN9lrfQJ--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/4a7551bcc3548e67-s.p.717db902.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/chunks/059797b39db6c7ce.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/c85a7d07e7b82c9a.js"/><script src="/_next/static/chunks/5564f493499345f1.js" async=""></script><script src="/_next/static/chunks/30cb146bc1e6f45f.js" async=""></script><script src="/_next/static/chunks/8082ab48faca5ea1.js" async=""></script><script src="/_next/static/chunks/turbopack-24a0e33035502c06.js" async=""></script><script src="/_next/static/chunks/ff1a16fafef87110.js" async=""></script><script src="/_next/static/chunks/7dd66bdf8a7e5707.js" async=""></script><script src="/_next/static/chunks/721ba8f4cbeb71c9.js" async=""></script><meta name="next-size-adjust" content=""/><title>RAG AI Chat with Llama 3.1 8B Instant</title><meta name="description" content="Try to Chat"/><link rel="icon" href="/favicon.ico?favicon.0b3bf435.ico" sizes="256x256" type="image/x-icon"/><script src="/_next/static/chunks/a6dad97d9634a72d.js" noModule=""></script></head><body class="quicksand_b7e087bf-module__3kZyda__variable antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"><div class="mx-auto w-full max-w-4xl py-8 px-4"><div class="text-center mb-8"><h1 class="text-4xl font-bold text-gray-800 mb-2">PIP FTUI</h1><p class="text-gray-600 mb-4">Powered by Llama 3.1 8B Instant</p><div class="flex justify-center gap-4 mt-4"><div class="bg-blue-500 text-white px-6 py-2 rounded-lg font-medium">💬 Chat Mode</div><a href="/rag"><div class="bg-white text-gray-700 border-2 border-gray-300 px-6 py-2 rounded-lg font-medium hover:border-purple-500 hover:text-purple-600 transition-colors cursor-pointer">📚 RAG Mode</div></a></div></div><div class="bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200"><div class="p-6 space-y-6"><div class="text-center text-gray-500 mt-8"><div class="text-6xl mb-4">💬</div><p class="text-lg">Start a conversation!</p><p class="text-sm">Type your message below to begin chatting.</p></div><div></div></div></div><form class="flex gap-4"><div class="flex-1 relative"><input placeholder="Type your message..." class="w-full rounded-2xl border-2 border-gray-200 px-6 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 disabled:bg-gray-50 disabled:cursor-not-allowed text-lg shadow-lg" value=""/></div><button type="submit" disabled="" class="rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 px-8 py-4 text-white font-medium hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-200">Send</button></form></div></div><!--$--><!--/$--><script src="/_next/static/chunks/c85a7d07e7b82c9a.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39756,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"default\"]\n3:I[37457,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"default\"]\n4:I[47257,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"ClientPageRoot\"]\n5:I[52683,[\"/_next/static/chunks/721ba8f4cbeb71c9.js\"],\"default\"]\n8:I[97367,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"OutletBoundary\"]\na:I[11533,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"AsyncMetadataOutlet\"]\nc:I[97367,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"ViewportBoundary\"]\ne:I[97367,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"MetadataBoundary\"]\nf:\"$Sreact.suspense\"\n11:I[68027,[],\"default\"]\n:HL[\"/_next/static/chunks/059797b39db6c7ce.css\",\"style\"]\n:HL[\"/_next/static/media/4a7551bcc3548e67-s.p.717db902.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"ha4rHZlxg3udBiN9lrfQJ\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/chunks/059797b39db6c7ce.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"quicksand_b7e087bf-module__3kZyda__variable antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[[\"$\",\"script\",\"script-0\",{\"src\":\"/_next/static/chunks/721ba8f4cbeb71c9.js\",\"async\":true,\"nonce\":\"$undefined\"}]],[\"$\",\"$L8\",null,{\"children\":[\"$L9\",[\"$\",\"$La\",null,{\"promise\":\"$@b\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$Le\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":\"$L10\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/chunks/059797b39db6c7ce.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:\"$0:f:0:1:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"12:I[27201,[\"/_next/static/chunks/ff1a16fafef87110.js\",\"/_next/static/chunks/7dd66bdf8a7e5707.js\"],\"IconMark\"]\nb:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"RAG AI Chat with Llama 3.1 8B Instant\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Try to Chat\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico?favicon.0b3bf435.ico\",\"sizes\":\"256x256\",\"type\":\"image/x-icon\"}],[\"$\",\"$L12\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"10:\"$b:metadata\"\n"])</script></body></html>