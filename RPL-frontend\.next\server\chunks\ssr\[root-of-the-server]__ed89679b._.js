module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},62212,a=>{a.n(a.i(66114))},55451,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/HomePage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/HomePage.tsx <module evaluation>","default")},54788,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/HomePage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/HomePage.tsx","default")},28884,a=>{"use strict";a.i(55451);var b=a.i(54788);a.n(b)},78764,(a,b,c)=>{},9488,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(7997),c=a.i(28884);function d(){return(0,b.jsx)(c.default,{})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ed89679b._.js.map