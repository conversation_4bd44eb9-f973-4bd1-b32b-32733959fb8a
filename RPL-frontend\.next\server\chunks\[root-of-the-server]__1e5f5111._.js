module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},95266,(e,t,r)=>{},92792,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>N,"routeModule",()=>y,"serverHooks",()=>T,"workAsyncStorage",()=>E,"workUnitAsyncStorage",()=>C],92792);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),h=e.i(26937),R=e.i(10372),f=e.i(93695);e.i(52474);var x=e.i(220);e.s(["GET",()=>m,"POST",()=>w],46108);let v="http://localhost:8000";async function w(e){try{let t=(await e.formData()).get("file");if(!t)return new Response(JSON.stringify({error:"No file provided"}),{status:400,headers:{"Content-Type":"application/json"}});let r=new FormData;r.append("file",t);let n=await fetch(`${v}/api/upload`,{method:"POST",body:r});if(!n.ok){let e=await n.json();return new Response(JSON.stringify({error:e.detail||"Failed to upload file"}),{status:n.status,headers:{"Content-Type":"application/json"}})}let a=await n.json();return new Response(JSON.stringify(a),{headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Error uploading file:",e),new Response(JSON.stringify({error:"Internal server error"}),{status:500,headers:{"Content-Type":"application/json"}})}}async function m(){try{let e=await fetch(`${v}/api/documents`);if(!e.ok)throw Error("Failed to fetch documents");let t=await e.json();return new Response(JSON.stringify(t),{headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Error fetching documents:",e),new Response(JSON.stringify({error:"Internal server error"}),{status:500,headers:{"Content-Type":"application/json"}})}}var g=e.i(46108);let y=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/documents/route",pathname:"/api/documents",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/documents/route.ts",nextConfigOutput:"",userland:g}),{workAsyncStorage:E,workUnitAsyncStorage:C,serverHooks:T}=y;function N(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:C})}async function A(e,t,n){var v;let w="/api/documents/route";w=w.replace(/\/index$/,"")||"/";let m=await y.prepare(e,t,{srcPage:w,multiZoneDraftMode:!1});if(!m)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:g,params:E,nextConfig:C,isDraftMode:T,prerenderManifest:N,routerServerContext:A,isOnDemandRevalidate:O,revalidateOnlyGenerated:b,resolvedPathname:S}=m,j=(0,s.normalizeAppPath)(w),P=!!(N.dynamicRoutes[j]||N.routes[S]);if(P&&!T){let e=!!N.routes[S],t=N.dynamicRoutes[j];if(t&&!1===t.fallback&&!e)throw new f.NoFallbackError}let k=null;!P||y.isDev||T||(k="/index"===(k=S)?"/":k);let _=!0===y.isDev||!P,q=P&&!_,H=e.method||"GET",I=(0,o.getTracer)(),U=I.getActiveScopeSpan(),D={params:E,prerenderManifest:N,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=C.experimental)?void 0:v.cacheLife,isRevalidate:q,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>y.onRequestError(e,t,n,A)},sharedContext:{buildId:g}},M=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(M,(0,l.signalFromNodeResponse)(t));try{let s=async r=>y.handle($,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=I.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${H} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${H} ${e.url}`)}),i=async o=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&O&&b&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=D.renderOpts.fetchMetrics;let l=D.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let d=D.renderOpts.collectedTags;if(!P)return await (0,p.sendResponse)(M,F,i,D.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[R.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=R.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,n=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=R.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await y.onRequestError(e,t,{routerKind:"App Router",routePath:w,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:q,isOnDemandRevalidate:O})},A),t}},f=await y.handleResponse({req:e,nextConfig:C,cacheKey:k,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:N,isRoutePPREnabled:!1,isOnDemandRevalidate:O,revalidateOnlyGenerated:b,responseGenerator:d,waitUntil:n.waitUntil});if(!P)return null;if((null==f||null==(i=f.value)?void 0:i.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==f||null==(l=f.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",O?"REVALIDATED":f.isMiss?"MISS":f.isStale?"STALE":"HIT"),T&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(f.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&P||v.delete(R.NEXT_CACHE_TAGS_HEADER),!f.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,h.getCacheControlHeader)(f.cacheControl)),await (0,p.sendResponse)(M,F,new Response(f.value.body,{headers:v,status:f.value.status||200})),null};U?await i(U):await I.withPropagatedContext(e.headers,()=>I.trace(d.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},i))}catch(t){if(t instanceof f.NoFallbackError||await y.onRequestError(e,t,{routerKind:"App Router",routePath:j,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:q,isOnDemandRevalidate:O})}),P)throw t;return await (0,p.sendResponse)(M,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__1e5f5111._.js.map