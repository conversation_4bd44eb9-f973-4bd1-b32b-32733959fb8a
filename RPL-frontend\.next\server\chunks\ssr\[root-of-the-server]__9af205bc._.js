module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},9317,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(87924),c=a.i(72131),d=a.i(38246),e=a.i(73885),f=a.i(56283);function g(){let[a,g]=(0,c.useState)([]),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(!1),[l,m]=(0,c.useState)([]),[n,o]=(0,c.useState)(!1),[p,q]=(0,c.useState)(!1),r=(0,c.useRef)(null),s=(0,c.useRef)(null);(0,c.useEffect)(()=>{r.current&&(r.current.scrollTop=r.current.scrollHeight)},[a]),(0,c.useEffect)(()=>{t()},[]);let t=async()=>{try{let a=await fetch("/api/documents");if(a.ok){let b=await a.json();m(b.documents.map(a=>({name:a})))}}catch(a){console.error("Error fetching documents:",a)}},u=async a=>{let b=a.target.files?.[0];if(!b)return;o(!0);let c=new FormData;c.append("file",b);try{let a=await fetch("/api/documents",{method:"POST",body:c});if(a.ok)await t(),alert("Document uploaded successfully!"),q(!1);else{let b=await a.json();alert(`Upload failed: ${b.error}`)}}catch(a){console.error("Error uploading file:",a),alert("Failed to upload document")}finally{o(!1),s.current&&(s.current.value="")}},v=async a=>{if(a.preventDefault(),!h.trim()||j)return;let b={id:Date.now().toString(),role:"user",content:h};g(a=>[...a,b]),i(""),k(!0);try{let a=await fetch("/api/rag",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:h,top_k:3})});if(!a.ok)throw Error("Failed to fetch response");let b=await a.json(),c={id:(Date.now()+1).toString(),role:"assistant",content:b.answer,sources:b.sources};g(a=>[...a,c])}catch(b){console.error("Error:",b);let a={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error. Please make sure you have uploaded documents and the backend is running."};g(b=>[...b,a])}finally{k(!1)}};return(0,b.jsxs)("div",{className:"min-h-screen flex flex-col",style:{background:"linear-gradient(135deg, #346ad5 0%, #4a7dd9 50%, #fae664 100%)"},children:[(0,b.jsx)(e.default,{}),(0,b.jsxs)("div",{className:"mx-auto w-full max-w-6xl py-8 px-4 mt-24 flex-grow",children:[(0,b.jsxs)("div",{className:"text-center mb-8",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-4xl font-bold text-white mb-2 font-[family-name:var(--font-comfortaa)]",children:"PIP FTUI - RAG Mode"}),(0,b.jsx)("h2",{className:"text-xl font-semibold text-yellow-100 font-[family-name:var(--font-comfortaa)]",children:"Retrieval-Augmented Generation"})]}),(0,b.jsx)("p",{className:"text-white/90 mb-4 text-lg mt-4 font-[family-name:var(--font-comfortaa)]",children:"Cari informasi FTUI berdasarkan dokumen yang tersedia"}),(0,b.jsxs)("div",{className:"flex justify-center gap-4 mt-6",children:[(0,b.jsx)(d.default,{href:"/chat",children:(0,b.jsx)("div",{className:"bg-[#fae664] text-[#346ad5] px-6 py-3 rounded-lg font-medium hover:bg-[#f5d93f] transition-colors cursor-pointer shadow-lg font-[family-name:var(--font-comfortaa)]",children:"💬 Chat Mode"})}),(0,b.jsx)("div",{className:"bg-white text-[#346ad5] px-6 py-3 rounded-lg font-medium shadow-lg font-[family-name:var(--font-comfortaa)]",children:"📚 RAG Mode"})]})]}),(0,b.jsxs)("div",{className:"flex gap-6",children:[(0,b.jsxs)("div",{className:"w-80 bg-white rounded-2xl shadow-xl p-6 h-[700px] border border-gray-200",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,b.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Documents"}),(0,b.jsx)("button",{onClick:()=>q(!p),className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:p?"Cancel":"+ Upload"})]}),p&&(0,b.jsxs)("div",{className:"mb-4 p-4 bg-purple-50 rounded-lg border-2 border-dashed border-purple-300",children:[(0,b.jsx)("input",{ref:s,type:"file",onChange:u,disabled:n,accept:".pdf,.txt,.docx,.md,.html",className:"w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-500 file:text-white hover:file:bg-purple-600 file:cursor-pointer"}),n&&(0,b.jsx)("p",{className:"text-sm text-purple-600 mt-2",children:"Uploading..."})]}),(0,b.jsx)("div",{className:"overflow-y-auto h-[calc(100%-100px)]",children:0===l.length?(0,b.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,b.jsx)("div",{className:"text-4xl mb-2",children:"📄"}),(0,b.jsx)("p",{className:"text-sm",children:"No documents yet"}),(0,b.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Upload a document to start"})]}):(0,b.jsx)("div",{className:"space-y-2",children:l.map((a,c)=>(0,b.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors",children:(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)("span",{className:"text-2xl",children:"📄"}),(0,b.jsx)("span",{className:"text-sm text-gray-700 truncate flex-1",children:a.name})]})},c))})})]}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("div",{ref:r,className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,b.jsxs)("div",{className:"p-6 space-y-6",children:[0===a.length?(0,b.jsxs)("div",{className:"text-center text-[#346ad5] mt-8",children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"📚"}),(0,b.jsx)("p",{className:"text-lg font-semibold",children:"Tanyakan tentang informasi FTUI"}),(0,b.jsx)("p",{className:"text-sm",children:"Anda hanya perlu mengirim pertanyaan yang ingin ditanyakan."})]}):a.map(a=>(0,b.jsx)("div",{className:`flex ${"user"===a.role?"justify-end":"justify-start"}`,children:(0,b.jsxs)("div",{className:`
                          max-w-[80%] rounded-2xl px-6 py-4 shadow-md
                          ${"user"===a.role?"bg-[#346ad5] text-white":"bg-white text-gray-800 border border-gray-200"}
                        `,children:[(0,b.jsx)("div",{className:`text-xs mb-2 font-medium ${"user"===a.role?"text-blue-100":"text-[#346ad5]"}`,children:"user"===a.role?"You":"PIP RAG Assistant"}),(0,b.jsx)("div",{className:`text-sm leading-relaxed whitespace-pre-wrap ${"user"===a.role?"text-white":"text-gray-700"}`,children:a.content}),a.sources&&a.sources.length>0&&(0,b.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-300",children:[(0,b.jsx)("p",{className:"text-xs font-semibold text-gray-600 mb-2",children:"Sources:"}),(0,b.jsx)("div",{className:"space-y-2",children:a.sources.map((a,c)=>(0,b.jsx)("div",{className:"text-xs text-gray-600 bg-white p-2 rounded border border-gray-200",children:a},c))})]})]})},a.id)),j&&(0,b.jsx)("div",{className:"flex justify-start",children:(0,b.jsx)("div",{className:"bg-white rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)("div",{className:"flex space-x-1",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce"}),(0,b.jsx)("div",{className:"w-2 h-2 bg-[#346ad5] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,b.jsx)("div",{className:"w-2 h-2 bg-[#fae664] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,b.jsx)("span",{className:"text-sm text-[#346ad5]",children:"Mencari di dokumen..."})]})})})]})}),(0,b.jsx)("form",{onSubmit:v,className:"relative flex-shrink-0",children:(0,b.jsxs)("div",{className:"bg-gradient-to-r from-[#5a6c7d] via-[#5a7a9d] to-[#4a6b8a] rounded-full shadow-[0_6px_24px_rgba(0,0,0,0.25)] flex items-center px-8 py-4",children:[(0,b.jsx)("input",{value:h,onChange:a=>i(a.target.value),placeholder:"ASK PIP...",disabled:j||0===l.length,className:"flex-grow bg-transparent text-white placeholder-white/70 focus:outline-none text-[17px] font-[family-name:var(--font-quicksand)] disabled:cursor-not-allowed"}),(0,b.jsx)("button",{type:"submit",disabled:j||!h.trim()||0===l.length,className:"ml-4 bg-[#ffd954] hover:bg-[#ffed4e] text-gray-900 rounded-full w-[50px] h-[50px] flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-[0_4px_12px_rgba(0,0,0,0.2)]","aria-label":"Send message",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})})]})]})]}),(0,b.jsx)(f.default,{})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9af205bc._.js.map