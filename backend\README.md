# Backend LlamaIndex RAG API

Backend Python untuk aplikasi RAG (Retrieval-Augmented Generation) menggunakan LlamaIndex dan Groq.

## Setup

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment Variables

Copy `.env.example` to `.env` dan isi dengan API key Anda:

```bash
cp .env.example .env
```

Edit `.env`:
```
GROQ_API_KEY=gsk_your_actual_api_key_here
```

Dapatkan GROQ API key gratis di: https://console.groq.com/

### 3. Jalankan Server

```bash
python main.py
```

Atau dengan uvicorn:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

Server akan berjalan di: http://localhost:8000

## API Endpoints

### Health Check
- `GET /` - Root endpoint
- `GET /health` - Health check

### Document Management
- `POST /api/upload` - Upload dokumen (PDF, TXT, DOCX, dll)
- `GET /api/documents` - List semua dokumen
- `DELETE /api/documents/{filename}` - Hapus dokumen
- `POST /api/rebuild-index` - Rebuild index

### Query
- `POST /api/query` - Query dokumen dengan RAG

#### Contoh Request:
```json
{
  "question": "Apa isi dokumen ini?",
  "top_k": 3
}
```

#### Contoh Response:
```json
{
  "answer": "Dokumen ini berisi informasi tentang...",
  "sources": [
    "Source text 1...",
    "Source text 2..."
  ]
}
```

## Struktur Folder

```
backend/
├── main.py              # Main FastAPI application
├── requirements.txt     # Python dependencies
├── .env                 # Environment variables (create from .env.example)
├── .env.example         # Example environment variables
├── data/                # Upload dokumen di sini
└── storage/             # Vector index storage (auto-generated)
```

## Testing API

Gunakan swagger UI di: http://localhost:8000/docs

Atau test dengan curl:

```bash
# Health check
curl http://localhost:8000/health

# Upload dokumen
curl -X POST "http://localhost:8000/api/upload" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf"

# Query
curl -X POST "http://localhost:8000/api/query" \
  -H "Content-Type: application/json" \
  -d '{"question": "What is this document about?"}'
```

## Supported Document Formats

- PDF (.pdf)
- Text (.txt)
- Word (.docx)
- Markdown (.md)
- HTML (.html)
- Dan format lainnya yang didukung SimpleDirectoryReader

## Troubleshooting

### Error: No module named 'torch'
Install torch secara manual:
```bash
pip install torch --index-url https://download.pytorch.org/whl/cpu
```

### Error: GROQ_API_KEY not found
Pastikan file `.env` sudah dibuat dan berisi GROQ_API_KEY yang valid.

### Port 8000 sudah digunakan
Ubah port di main.py atau jalankan dengan port berbeda:
```bash
uvicorn main:app --reload --port 8001
```
