(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},98183,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function l(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return l},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return a}})},95057,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return l}});let n=e.r(90809)._(e.r(98183)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",s=e.hash||"",i=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==u?(u="//"+(u||""),l&&"/"!==l[0]&&(l="/"+l)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},18581,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(71645);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18967,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return g},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return b},NormalizeError:function(){return m},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return h},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return u},getLocationOrigin:function(){return s},getURL:function(){return i},isAbsoluteUrl:function(){return l},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return v}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>a.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=s();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,h=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class g extends Error{}class m extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=e.r(18967),o=e.r(52817);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},84508,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},22016,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return m},useLinkStatus:function(){return b}});let n=e.r(90809),o=e.r(43476),a=n._(e.r(71645)),l=e.r(95057),s=e.r(8372),i=e.r(18581),u=e.r(18967),c=e.r(5550);e.r(33525);let d=e.r(91949),f=e.r(73668),p=e.r(99781);e.r(84508);let h=e.r(65165);function g(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function m(e){var t;let r,n,l,[m,b]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),x=(0,a.useRef)(null),{href:v,as:j,children:N,prefetch:P=null,passHref:w,replace:O,shallow:S,scroll:_,onClick:E,onMouseEnter:T,onTouchStart:C,legacyBehavior:R=!1,onNavigate:A,ref:I,unstable_dynamicOnHover:M,...L}=e;r=N,R&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let U=a.default.useContext(s.AppRouterContext),k=!1!==P,D=!1!==P?null===(t=P)||"auto"===t?h.FetchStrategy.PPR:h.FetchStrategy.Full:h.FetchStrategy.PPR,{href:F,as:B}=a.default.useMemo(()=>{let e=g(v);return{href:e,as:j?g(j):e}},[v,j]);R&&(n=a.default.Children.only(r));let K=R?n&&"object"==typeof n&&n.ref:I,z=a.default.useCallback(e=>(null!==U&&(x.current=(0,d.mountLinkInstance)(e,F,U,D,k,b)),()=>{x.current&&((0,d.unmountLinkForCurrentNavigation)(x.current),x.current=null),(0,d.unmountPrefetchableInstance)(e)}),[k,F,U,D,b]),W={ref:(0,i.useMergedRef)(z,K),onClick(e){R||"function"!=typeof E||E(e),R&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),U&&(e.defaultPrevented||function(e,t,r,n,o,l,s){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==l||l,n.current)})}}(e,F,B,x,O,_,A))},onMouseEnter(e){R||"function"!=typeof T||T(e),R&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),U&&k&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){R||"function"!=typeof C||C(e),R&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),U&&k&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,u.isAbsoluteUrl)(B)?W.href=B:R&&!w&&("a"!==n.type||"href"in n.props)||(W.href=(0,c.addBasePath)(B)),l=R?a.default.cloneElement(n,W):(0,o.jsx)("a",{...L,...W,children:r}),(0,o.jsx)(y.Provider,{value:m,children:l})}let y=(0,a.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,a.useContext)(y);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},52683,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(43476),r=e.i(71645),n=e.i(22016);function o(){let[e,o]=(0,r.useState)([]),[a,l]=(0,r.useState)(""),[s,i]=(0,r.useState)(!1),u=(0,r.useRef)(null);(0,r.useEffect)(()=>{var e;null==(e=u.current)||e.scrollIntoView({behavior:"smooth"})},[e]);let c=async t=>{if(t.preventDefault(),!a.trim()||s)return;let r={id:Date.now().toString(),role:"user",content:a};o(e=>[...e,r]),l(""),i(!0);let n={id:(Date.now()+1).toString(),role:"assistant",content:""};o(e=>[...e,n]);try{var u;let t=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...e,r]})});if(!t.ok)throw Error("Failed to fetch response");let a=null==(u=t.body)?void 0:u.getReader(),l=new TextDecoder;if(a)for(;;){let{done:e,value:t}=await a.read();if(e)break;for(let e of l.decode(t).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)break;try{let e=JSON.parse(t);e.content&&o(t=>t.map(t=>t.id===n.id?{...t,content:t.content+e.content}:t))}catch(e){}}}}catch(e){console.error("Error:",e),o(e=>e.map(e=>e.id===n.id?{...e,content:"Sorry, I encountered an error. Please try again."}:e))}finally{i(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,t.jsxs)("div",{className:"mx-auto w-full max-w-4xl py-8 px-4",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"PIP FTUI"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Powered by Llama 3.1 8B Instant"}),(0,t.jsxs)("div",{className:"flex justify-center gap-4 mt-4",children:[(0,t.jsx)("div",{className:"bg-blue-500 text-white px-6 py-2 rounded-lg font-medium",children:"💬 Chat Mode"}),(0,t.jsx)(n.default,{href:"/rag",children:(0,t.jsx)("div",{className:"bg-white text-gray-700 border-2 border-gray-300 px-6 py-2 rounded-lg font-medium hover:border-purple-500 hover:text-purple-600 transition-colors cursor-pointer",children:"📚 RAG Mode"})})]})]}),(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl mb-6 h-[600px] overflow-y-auto border border-gray-200",children:(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[0===e.length?(0,t.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"💬"}),(0,t.jsx)("p",{className:"text-lg",children:"Start a conversation!"}),(0,t.jsx)("p",{className:"text-sm",children:"Type your message below to begin chatting."})]}):e.map(e=>(0,t.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,t.jsxs)("div",{className:"\n                      max-w-[80%] rounded-2xl px-6 py-4 shadow-md\n                      ".concat("user"===e.role?"bg-gradient-to-r from-blue-500 to-blue-600 text-white":"bg-gray-100 text-gray-800 border border-gray-200","\n                    "),children:[(0,t.jsx)("div",{className:"text-xs mb-2 font-medium ".concat("user"===e.role?"text-blue-100":"text-gray-500"),children:"user"===e.role?"You":"Llama 3.1 8B Instant"}),(0,t.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-wrap ".concat("user"===e.role?"text-white":"text-gray-700"),children:e.content})]})},e.id)),s&&(0,t.jsx)("div",{className:"flex justify-start",children:(0,t.jsx)("div",{className:"bg-gray-100 rounded-2xl px-6 py-4 shadow-md border border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Thinking..."})]})})}),(0,t.jsx)("div",{ref:u})]})}),(0,t.jsxs)("form",{onSubmit:c,className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1 relative",children:(0,t.jsx)("input",{value:a,onChange:e=>l(e.target.value),placeholder:"Type your message...",disabled:s,className:"w-full rounded-2xl border-2 border-gray-200 px-6 py-4 text-gray-800 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 disabled:bg-gray-50 disabled:cursor-not-allowed text-lg shadow-lg"})}),(0,t.jsx)("button",{type:"submit",disabled:s||!a.trim(),className:"rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600 px-8 py-4 text-white font-medium hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-200",children:s?"Sending...":"Send"})]})]})})}}]);